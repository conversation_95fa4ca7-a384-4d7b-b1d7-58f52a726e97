import { ref } from 'vue';
import { showToast } from 'vant';
import { useRouter, useRoute } from 'vue-router';
import SectionHeader from '../../../components/SectionHeader.vue'; // 引入通用标题组件
const router = useRouter();
const route = useRoute();
// 第一排功能按钮数据（2个）
const firstRowItems = ref([
    {
        icon: 'manager-o',
        text: '领导信息',
        bgColor: '#e8f4ff',
        iconColor: '#1989fa',
        routeName: 'Leader'
    },
    {
        icon: 'friends-o',
        text: '名中医信息',
        bgColor: '#e8f7ff',
        iconColor: '#07c160',
        routeName: 'Doctor'
    }
]);
// 第二排功能按钮数据（3个）
const secondRowItems = ref([
    {
        icon: 'shop-o',
        text: '特色制剂',
        bgColor: '#fef2e8',
        iconColor: '#ff976a',
        routeName: 'Medicine'
    },
    {
        icon: 'cluster-o',
        text: '特色科室',
        bgColor: '#f5f5f5',
        iconColor: '#ee0a24',
        routeName: 'Dept'
    },
    {
        icon: 'star-o',
        text: '重点科室',
        bgColor: '#f0f9eb',
        iconColor: '#8bc34a',
        routeName: 'MainDept'
    }
]);
// 判断是否是当前激活的路由
const isActiveRoute = (item) => {
    return route.name === item.routeName;
};
// 获取图标颜色
const getIconColor = (item) => {
    return isActiveRoute(item) ? '#fff' : item.iconColor;
};
// 获取图标样式
const getIconStyle = (item) => {
    if (isActiveRoute(item)) {
        return {
            background: `linear-gradient(135deg, ${item.iconColor}, ${item.iconColor}dd)`
        };
    }
    return {
        backgroundColor: item.bgColor
    };
};
// 功能按钮点击事件
const onFunctionClick = (item) => {
    console.log(`点击了${item.text}`);
    // 根据功能项跳转到对应的分类页面
    switch (item.text) {
        case '领导信息':
            router.push({ name: 'Leader', params: { type: 'leader' } });
            break;
        case '名中医信息':
            router.push({ name: 'Doctor', params: { type: 'doctor' } });
            break;
        case '特色制剂':
            router.push({ name: 'Medicine', params: { type: 'medicine' } });
            break;
        case '特色科室':
            router.push({ name: 'Dept', params: { type: 'department' } });
            break;
        case '重点科室':
            router.push({ name: 'MainDept', params: { type: 'main-department' } });
            break;
        default:
            showToast(`点击了${item.text}`);
    }
};
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
/** @type {__VLS_StyleScopedClasses['section-title']} */ ;
/** @type {__VLS_StyleScopedClasses['test-item']} */ ;
/** @type {__VLS_StyleScopedClasses['second-row']} */ ;
/** @type {__VLS_StyleScopedClasses['van-grid-item']} */ ;
/** @type {__VLS_StyleScopedClasses['third-row']} */ ;
/** @type {__VLS_StyleScopedClasses['van-grid-item']} */ ;
/** @type {__VLS_StyleScopedClasses['grid-item-content']} */ ;
/** @type {__VLS_StyleScopedClasses['icon-wrapper']} */ ;
/** @type {__VLS_StyleScopedClasses['grid-item-content']} */ ;
/** @type {__VLS_StyleScopedClasses['icon-wrapper']} */ ;
/** @type {__VLS_StyleScopedClasses['icon-wrapper']} */ ;
/** @type {__VLS_StyleScopedClasses['active']} */ ;
/** @type {__VLS_StyleScopedClasses['grid-text']} */ ;
/** @type {__VLS_StyleScopedClasses['active']} */ ;
/** @type {__VLS_StyleScopedClasses['grid-item-content']} */ ;
/** @type {__VLS_StyleScopedClasses['active']} */ ;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "section-container" },
});
/** @type {[typeof SectionHeader, ]} */ ;
// @ts-ignore
const __VLS_0 = __VLS_asFunctionalComponent(SectionHeader, new SectionHeader({
    title: "常用功能",
    showMore: (false),
}));
const __VLS_1 = __VLS_0({
    title: "常用功能",
    showMore: (false),
}, ...__VLS_functionalComponentArgsRest(__VLS_0));
const __VLS_3 = {}.VanGrid;
/** @type {[typeof __VLS_components.VanGrid, typeof __VLS_components.vanGrid, typeof __VLS_components.VanGrid, typeof __VLS_components.vanGrid, ]} */ ;
// @ts-ignore
const __VLS_4 = __VLS_asFunctionalComponent(__VLS_3, new __VLS_3({
    columnNum: (2),
    border: (false),
    gutter: (10),
    ...{ class: "function-grid first-row" },
}));
const __VLS_5 = __VLS_4({
    columnNum: (2),
    border: (false),
    gutter: (10),
    ...{ class: "function-grid first-row" },
}, ...__VLS_functionalComponentArgsRest(__VLS_4));
__VLS_6.slots.default;
for (const [item, index] of __VLS_getVForSourceType((__VLS_ctx.firstRowItems))) {
    const __VLS_7 = {}.VanGridItem;
    /** @type {[typeof __VLS_components.VanGridItem, typeof __VLS_components.vanGridItem, typeof __VLS_components.VanGridItem, typeof __VLS_components.vanGridItem, ]} */ ;
    // @ts-ignore
    const __VLS_8 = __VLS_asFunctionalComponent(__VLS_7, new __VLS_7({
        key: (index),
        ...{ class: "animate__animated animate__fadeInUp" },
        ...{ style: ({ animationDelay: index * 0.05 + 's' }) },
    }));
    const __VLS_9 = __VLS_8({
        key: (index),
        ...{ class: "animate__animated animate__fadeInUp" },
        ...{ style: ({ animationDelay: index * 0.05 + 's' }) },
    }, ...__VLS_functionalComponentArgsRest(__VLS_8));
    __VLS_10.slots.default;
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ onClick: (...[$event]) => {
                __VLS_ctx.onFunctionClick(item);
            } },
        ...{ class: "grid-item-content" },
        ...{ class: ({ active: __VLS_ctx.isActiveRoute(item) }) },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "icon-wrapper" },
        ...{ class: ({ active: __VLS_ctx.isActiveRoute(item) }) },
        ...{ style: (__VLS_ctx.getIconStyle(item)) },
    });
    const __VLS_11 = {}.VanIcon;
    /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
    // @ts-ignore
    const __VLS_12 = __VLS_asFunctionalComponent(__VLS_11, new __VLS_11({
        name: (item.icon),
        color: (__VLS_ctx.getIconColor(item)),
        size: "24",
    }));
    const __VLS_13 = __VLS_12({
        name: (item.icon),
        color: (__VLS_ctx.getIconColor(item)),
        size: "24",
    }, ...__VLS_functionalComponentArgsRest(__VLS_12));
    __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
        ...{ class: "grid-text" },
        ...{ class: ({ active: __VLS_ctx.isActiveRoute(item) }) },
    });
    (item.text);
    var __VLS_10;
}
var __VLS_6;
const __VLS_15 = {}.VanGrid;
/** @type {[typeof __VLS_components.VanGrid, typeof __VLS_components.vanGrid, typeof __VLS_components.VanGrid, typeof __VLS_components.vanGrid, ]} */ ;
// @ts-ignore
const __VLS_16 = __VLS_asFunctionalComponent(__VLS_15, new __VLS_15({
    columnNum: (3),
    border: (false),
    gutter: (10),
    ...{ class: "function-grid second-row" },
}));
const __VLS_17 = __VLS_16({
    columnNum: (3),
    border: (false),
    gutter: (10),
    ...{ class: "function-grid second-row" },
}, ...__VLS_functionalComponentArgsRest(__VLS_16));
__VLS_18.slots.default;
for (const [item, index] of __VLS_getVForSourceType((__VLS_ctx.secondRowItems))) {
    const __VLS_19 = {}.VanGridItem;
    /** @type {[typeof __VLS_components.VanGridItem, typeof __VLS_components.vanGridItem, typeof __VLS_components.VanGridItem, typeof __VLS_components.vanGridItem, ]} */ ;
    // @ts-ignore
    const __VLS_20 = __VLS_asFunctionalComponent(__VLS_19, new __VLS_19({
        key: (index),
        ...{ class: "animate__animated animate__fadeInUp" },
        ...{ style: ({ animationDelay: (index + 2) * 0.05 + 's' }) },
    }));
    const __VLS_21 = __VLS_20({
        key: (index),
        ...{ class: "animate__animated animate__fadeInUp" },
        ...{ style: ({ animationDelay: (index + 2) * 0.05 + 's' }) },
    }, ...__VLS_functionalComponentArgsRest(__VLS_20));
    __VLS_22.slots.default;
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ onClick: (...[$event]) => {
                __VLS_ctx.onFunctionClick(item);
            } },
        ...{ class: "grid-item-content" },
        ...{ class: ({ active: __VLS_ctx.isActiveRoute(item) }) },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "icon-wrapper" },
        ...{ class: ({ active: __VLS_ctx.isActiveRoute(item) }) },
        ...{ style: (__VLS_ctx.getIconStyle(item)) },
    });
    const __VLS_23 = {}.VanIcon;
    /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
    // @ts-ignore
    const __VLS_24 = __VLS_asFunctionalComponent(__VLS_23, new __VLS_23({
        name: (item.icon),
        color: (__VLS_ctx.getIconColor(item)),
        size: "24",
    }));
    const __VLS_25 = __VLS_24({
        name: (item.icon),
        color: (__VLS_ctx.getIconColor(item)),
        size: "24",
    }, ...__VLS_functionalComponentArgsRest(__VLS_24));
    __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
        ...{ class: "grid-text" },
        ...{ class: ({ active: __VLS_ctx.isActiveRoute(item) }) },
    });
    (item.text);
    var __VLS_22;
}
var __VLS_18;
/** @type {__VLS_StyleScopedClasses['section-container']} */ ;
/** @type {__VLS_StyleScopedClasses['function-grid']} */ ;
/** @type {__VLS_StyleScopedClasses['first-row']} */ ;
/** @type {__VLS_StyleScopedClasses['animate__animated']} */ ;
/** @type {__VLS_StyleScopedClasses['animate__fadeInUp']} */ ;
/** @type {__VLS_StyleScopedClasses['grid-item-content']} */ ;
/** @type {__VLS_StyleScopedClasses['active']} */ ;
/** @type {__VLS_StyleScopedClasses['icon-wrapper']} */ ;
/** @type {__VLS_StyleScopedClasses['active']} */ ;
/** @type {__VLS_StyleScopedClasses['grid-text']} */ ;
/** @type {__VLS_StyleScopedClasses['active']} */ ;
/** @type {__VLS_StyleScopedClasses['function-grid']} */ ;
/** @type {__VLS_StyleScopedClasses['second-row']} */ ;
/** @type {__VLS_StyleScopedClasses['animate__animated']} */ ;
/** @type {__VLS_StyleScopedClasses['animate__fadeInUp']} */ ;
/** @type {__VLS_StyleScopedClasses['grid-item-content']} */ ;
/** @type {__VLS_StyleScopedClasses['active']} */ ;
/** @type {__VLS_StyleScopedClasses['icon-wrapper']} */ ;
/** @type {__VLS_StyleScopedClasses['active']} */ ;
/** @type {__VLS_StyleScopedClasses['grid-text']} */ ;
/** @type {__VLS_StyleScopedClasses['active']} */ ;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            SectionHeader: SectionHeader,
            firstRowItems: firstRowItems,
            secondRowItems: secondRowItems,
            isActiveRoute: isActiveRoute,
            getIconColor: getIconColor,
            getIconStyle: getIconStyle,
            onFunctionClick: onFunctionClick,
        };
    },
});
export default (await import('vue')).defineComponent({
    setup() {
        return {};
    },
});
; /* PartiallyEnd: #4569/main.vue */
