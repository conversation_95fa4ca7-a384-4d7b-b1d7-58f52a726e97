import type { HospitalNewsItem, NewsCardData } from '../types/news';

/**
 * 将API数据转换为NewsCard组件需要的格式
 * @param item 医院新闻API数据
 * @returns NewsCard组件数据格式
 */
export function transformHospitalNewsToNewsCard(item: HospitalNewsItem): NewsCardData {
  return {
    id: item.id,
    name: item.name,
    desc: item.desc,
    thumbnail: item.thumbnail,
    category: '医院新闻', // 固定分类
    publishDate: item.create_time,
    views: item.viewtimes ? parseInt(item.viewtimes) : undefined
  };
}

/**
 * 批量转换医院新闻数据
 * @param items 医院新闻API数据数组
 * @returns NewsCard组件数据格式数组
 */
export function transformHospitalNewsListToNewsCard(items: HospitalNewsItem[]): NewsCardData[] {
  return items.map(transformHospitalNewsToNewsCard);
}

/**
 * 按创建时间倒序排序医院新闻数据
 * @param items 医院新闻数据数组
 * @returns 按创建时间倒序排序的数组
 */
export function sortHospitalNewsByCreateTime(items: HospitalNewsItem[]): HospitalNewsItem[] {
  return [...items].sort((a, b) => {
    // 处理 create_time 为空的情况
    if (!a.create_time && !b.create_time) return 0;
    if (!a.create_time) return 1; // 没有时间的排在后面
    if (!b.create_time) return -1; // 没有时间的排在后面
    
    // 按时间倒序排序（最新的在前）
    return new Date(b.create_time).getTime() - new Date(a.create_time).getTime();
  });
}

/**
 * 按点击率(浏览次数)倒序排序视频数据
 * @param items 视频数据数组
 * @returns 按点击率倒序排序的数组
 */
export function sortVideosByViewCount(items: any[]): any[] {
  console.log('开始视频排序，原始数据:', items.map(item => ({
    id: item.id,
    name: item.name,
    viewtimes_display: item.viewtimes_display,
    viewtimes: item.viewtimes,
    views: item.views
  })));

  return [...items].sort((a, b) => {
    // 获取点击率数值，主要使用 viewtimes_display 字段
    const getViewCount = (item: any): number => {
      let count = 0;
      
      // 优先使用 viewtimes_display 字段
      if (item.viewtimes_display !== undefined && item.viewtimes_display !== null) {
        if (typeof item.viewtimes_display === 'number') {
          count = item.viewtimes_display;
        } else if (typeof item.viewtimes_display === 'string') {
          // 使用正则表达式提取数字，支持 "447"、"447次"、"447 views" 等格式
          const match = item.viewtimes_display.match(/\d+/);
          if (match) {
            const parsed = parseInt(match[0]);
            count = isNaN(parsed) ? 0 : parsed;
          }
        }
      }
      
      // 如果 viewtimes_display 没有获取到值，尝试其他字段
      if (count === 0) {
        // 备选：使用 views 字段
        if (item.views !== undefined && item.views !== null) {
          if (typeof item.views === 'number') {
            count = item.views;
          } else if (typeof item.views === 'string') {
            const match = item.views.match(/\d+/);
            if (match) {
              const parsed = parseInt(match[0]);
              count = isNaN(parsed) ? 0 : parsed;
            }
          }
        }
        
        // 备选：使用 viewtimes 字段
        if (count === 0 && item.viewtimes !== undefined && item.viewtimes !== null) {
          if (typeof item.viewtimes === 'number') {
            count = item.viewtimes;
          } else if (typeof item.viewtimes === 'string') {
            const match = item.viewtimes.match(/\d+/);
            if (match) {
              const parsed = parseInt(match[0]);
              count = isNaN(parsed) ? 0 : parsed;
            }
          }
        }
      }
      
      console.log(`ID ${item.id} (${item.name}) - 点击率解析结果: ${count}`, {
        viewtimes_display: item.viewtimes_display,
        views: item.views,
        viewtimes: item.viewtimes,
        parsed: count
      });
      
      return count;
    };
    
    const aViews = getViewCount(a);
    const bViews = getViewCount(b);
    
    // 按点击率倒序排序（点击率高的在前）
    const result = bViews - aViews;
    
    console.log(`排序比较: ID ${a.id}(${aViews}) vs ID ${b.id}(${bViews}) = ${result}`);
    
    return result;
  });
}

/**
 * 格式化日期显示
 * @param dateString 日期字符串
 * @returns 格式化后的日期
 */
export function formatNewsDate(dateString?: string): string | undefined {
  if (!dateString) return undefined;
  
  try {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}.${month}.${day}`;
  } catch {
    return dateString;
  }
} 