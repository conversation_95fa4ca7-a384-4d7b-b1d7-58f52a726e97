import { ref, watch, onMounted, nextTick } from 'vue';
import { formatActivityTimeRange, formatDeadline } from '../utils/dateTime';
const scrollContainerRef = ref(null);
const getScrollContainer = () => scrollContainerRef.value;
onMounted(() => {
    nextTick(() => {
        console.log('Activity滚动容器:', scrollContainerRef.value);
    });
});
const props = withDefaults(defineProps(), {
    emptyText: '暂无活动',
    useInfiniteScroll: true,
    finished: false,
});
const emit = defineEmits(['load-more', 'card-click', 'share', 'register']);
const internalLoading = ref(false);
watch(() => props.loading, (val) => {
    internalLoading.value = val;
}, { immediate: true });
const onLoadMore = () => {
    console.log('触发加载更多活动');
    emit('load-more');
};
// 获取活动状态
const getActivityStatus = (item) => {
    if (isExpired(item)) {
        return '报名已截止';
    }
    // 如果有明确的状态字段，优先使用
    if (item.status) {
        return item.status;
    }
    // 对于"正在报名中的活动"列表，默认状态应该是报名中
    // 我们可以通过其他字段来判断状态
    const now = new Date();
    // 检查活动时间
    const startDate = item.startdate ? new Date(item.startdate) : null;
    const endDate = item.enddate ? new Date(item.enddate) : null;
    if (startDate && endDate) {
        if (now < startDate) {
            return '报名中'; // 活动还没开始，处于报名阶段
        }
        else if (now >= startDate && now <= endDate) {
            return '进行中'; // 活动正在进行
        }
    }
    // 默认状态为报名中
    return '报名中';
};
// 获取状态标签类型
const getStatusTagType = (item) => {
    const status = getActivityStatus(item);
    switch (status) {
        case '进行中':
            return 'success';
        case '即将开始':
            return 'warning';
        case '已结束':
        case '报名已截止':
            return 'default';
        case '报名中':
            return 'primary';
        default:
            return 'primary';
    }
};
// 格式化活动时间 - 使用统一的时间格式化工具
const formatActivityTime = (item) => {
    if (item.startdate && item.enddate) {
        return formatActivityTimeRange(item.startdate, item.enddate);
    }
    else if (item.start_time && item.end_time) {
        return formatActivityTimeRange(item.start_time, item.end_time);
    }
    else if (item.startdate) {
        return formatActivityTimeRange(item.startdate, '');
    }
    else if (item.start_time) {
        return formatActivityTimeRange(item.start_time, '');
    }
    return '时间待定';
};
// 格式化参与人数
const formatParticipantCount = (item) => {
    if (item.current_participants !== undefined && item.max_participants !== undefined) {
        return `${item.current_participants}/${item.max_participants}人`;
    }
    else if (item.participant_count !== undefined) {
        return `${item.participant_count}人已报名`;
    }
    else if (item.nums !== undefined) {
        return `${item.nums}人已报名`;
    }
    else if (item.max_participants !== undefined) {
        return `限${item.max_participants}人`;
    }
    return '暂无数据';
};
// 格式化活动费用
const formatActivityFee = (item) => {
    if (item.isfree === '免费' || item.price === 0) {
        return '免费';
    }
    else if (item.price !== undefined) {
        return `¥${item.price}`;
    }
    else if (item.isfree) {
        return item.isfree;
    }
    return '免费'; // 默认静态数据
};
// 获取活动形式图标
const getActivityFormIcon = (item) => {
    const form = item.activity_type || item.activity_form || item.form_type || '';
    switch (form) {
        case '在线直播':
        case 'online_live':
            return 'play-circle-o';
        case '线下组织':
        case 'offline_organize':
            return 'shop-o';
        case '在线答题':
        case 'online_quiz':
            return 'edit';
        case '线上活动':
        case 'online_activity':
            return 'desktop-o';
        case '混合活动':
        case 'hybrid_activity':
            return 'exchange';
        default:
            return 'medal-o';
    }
};
// 获取活动形式样式类
const getActivityFormClass = (item) => {
    const form = item.activity_type || item.activity_form || item.form_type || '';
    switch (form) {
        case '在线直播':
        case 'online_live':
            return 'form-live';
        case '线下组织':
        case 'offline_organize':
            return 'form-offline';
        case '在线答题':
        case 'online_quiz':
            return 'form-quiz';
        case '线上活动':
        case 'online_activity':
            return 'form-online';
        case '混合活动':
        case 'hybrid_activity':
            return 'form-hybrid';
        default:
            return 'form-default';
    }
};
// 格式化活动形式
const formatActivityForm = (item) => {
    const form = item.activity_type || item.activity_form || item.form_type || '';
    // 如果是英文代码，转换为中文
    const formMap = {
        'online_live': '在线直播',
        'offline_organize': '线下组织',
        'online_quiz': '在线答题',
        'online_activity': '线上活动',
        'hybrid_activity': '混合活动'
    };
    return formMap[form] || form || '暂无数据';
};
// 判断活动是否已过期
const isExpired = (item) => {
    const deadlineStr = item.registration_deadline || item.dieline || item.deadline;
    if (!deadlineStr) {
        return false;
    }
    const regex = /^(\d{2})\/(\d{2}) (\d{2}):(\d{2})$/;
    const match = deadlineStr.match(regex);
    if (match) {
        const now = new Date();
        const year = now.getFullYear();
        const month = parseInt(match[1], 10) - 1;
        const day = parseInt(match[2], 10);
        const hours = parseInt(match[3], 10);
        const minutes = parseInt(match[4], 10);
        const deadlineDate = new Date(year, month, day, hours, minutes);
        return deadlineDate < now;
    }
    else {
        const deadlineDate = new Date(deadlineStr);
        if (isNaN(deadlineDate.getTime())) {
            return false;
        }
        return deadlineDate < new Date();
    }
};
// 判断活动数据是否完整
const isActivityDataComplete = (item) => {
    return item && item.name && item.name.trim() !== '';
};
// 处理转发按钮点击
const handleShare = (item) => {
    if (!isActivityDataComplete(item)) {
        return;
    }
    emit('share', item);
};
// 处理报名按钮点击
const handleRegister = (item) => {
    emit('register', item);
};
// 判断是否可以报名
const canRegister = (item) => {
    if (isExpired(item)) {
        return false;
    }
    if (item.current_participants && item.max_participants &&
        item.current_participants >= item.max_participants) {
        return false;
    }
    return true;
};
// 获取报名按钮类型
const getRegisterButtonType = (item) => {
    if (!canRegister(item)) {
        return 'default';
    }
    const status = getActivityStatus(item);
    if (status === '报名中') {
        return 'primary';
    }
    else if (status === '进行中') {
        return 'success';
    }
    return 'primary';
};
// 获取报名按钮图标
const getRegisterButtonIcon = (item) => {
    if (!canRegister(item)) {
        return 'cross';
    }
    const status = getActivityStatus(item);
    if (status === '报名中') {
        return 'add-o';
    }
    else if (status === '进行中') {
        return 'play-circle-o';
    }
    return 'add-o';
};
// 获取报名按钮文本
const getRegisterButtonText = (item) => {
    if (isExpired(item)) {
        return '报名已截止';
    }
    if (!canRegister(item)) {
        if (item.current_participants && item.max_participants &&
            item.current_participants >= item.max_participants) {
            return '报名满员';
        }
        return '无法报名';
    }
    const status = getActivityStatus(item);
    if (status === '报名中') {
        return '立即报名';
    }
    else if (status === '进行中') {
        return '正在进行';
    }
    return '立即报名';
};
// 格式化活动地点
const formatActivityLocation = (item) => {
    return item.location || item.address || '暂无数据';
};
// 格式化报名截止时间
const formatRegistrationDeadline = (item) => {
    const deadline = item.dieline || item.registration_deadline || item.deadline;
    if (!deadline)
        return '暂无数据';
    return formatDeadline(deadline);
};
// 格式化活动主办方
const formatOrganizer = (item) => {
    return item.leader || item.organizer || '暂无数据';
};
// 格式化联系方式
const formatContact = (item) => {
    return item.contact || '暂无数据';
};
// 获取高亮样式
const getHighlightStyle = (type) => {
    const styles = {
        time: { backgroundColor: '#e8f4ff', color: '#4b8bf4' },
        category: { backgroundColor: '#e6f7ef', color: '#059669' },
        deadline: { backgroundColor: '#e8f4ff', color: '#4b8bf4' }, // 使用与 'time' 类似的蓝色系
        contact: { backgroundColor: '#fffbe6', color: '#fa8c16' },
        location: { backgroundColor: '#f0f5ff', color: '#2f54eb' },
        participants: { backgroundColor: '#f6ffed', color: '#52c41a' },
        form: { backgroundColor: '#e6fffb', color: '#13c2c2' },
        fee: { backgroundColor: '#fff0f6', color: '#eb2f96' },
        organizer: { backgroundColor: '#f9f0ff', color: '#722ed1' },
    };
    const baseStyle = {
        padding: '8px 12px',
        borderRadius: '8px',
        marginBottom: '12px',
    };
    // 如果类型存在于样式中，则返回合并后的样式
    if (type in styles) {
        return { ...baseStyle, ...styles[type] };
    }
    // 否则，返回一个默认的、不带背景色的样式对象
    return {
        padding: '0',
        borderRadius: '0',
        marginBottom: '16px', // 保持与其他项的间距
        backgroundColor: 'transparent',
        color: '#333' // 默认文本颜色
    };
};
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_withDefaultsArg = (function (t) { return t; })({
    emptyText: '暂无活动',
    useInfiniteScroll: true,
    finished: false,
});
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
/** @type {__VLS_StyleScopedClasses['activity-card']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-card']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-card']} */ ;
/** @type {__VLS_StyleScopedClasses['expired']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-card']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-image']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item-time']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item-time']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item-time']} */ ;
/** @type {__VLS_StyleScopedClasses['van-icon']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item-category']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item-category']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-value']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item-category']} */ ;
/** @type {__VLS_StyleScopedClasses['van-icon']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item-deadline']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item-deadline']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-value']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item-deadline']} */ ;
/** @type {__VLS_StyleScopedClasses['van-icon']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item-contact']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item-contact']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-value']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item-contact']} */ ;
/** @type {__VLS_StyleScopedClasses['van-icon']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-value']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-heat']} */ ;
/** @type {__VLS_StyleScopedClasses['van-icon']} */ ;
/** @type {__VLS_StyleScopedClasses['share-button']} */ ;
/** @type {__VLS_StyleScopedClasses['register-button']} */ ;
/** @type {__VLS_StyleScopedClasses['register-button']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-image']} */ ;
/** @type {__VLS_StyleScopedClasses['card-content']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-title']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-description']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item']} */ ;
/** @type {__VLS_StyleScopedClasses['van-icon']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-label']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-value']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-tags']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-tag']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-actions']} */ ;
/** @type {__VLS_StyleScopedClasses['action-button']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-card']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-image']} */ ;
/** @type {__VLS_StyleScopedClasses['animate__fadeInUp']} */ ;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "activity-common-card-list" },
    ref: "scrollContainerRef",
});
/** @type {typeof __VLS_ctx.scrollContainerRef} */ ;
if (__VLS_ctx.loading && !__VLS_ctx.items.length) {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "loading-container" },
    });
    const __VLS_0 = {}.VanLoading;
    /** @type {[typeof __VLS_components.VanLoading, typeof __VLS_components.vanLoading, typeof __VLS_components.VanLoading, typeof __VLS_components.vanLoading, ]} */ ;
    // @ts-ignore
    const __VLS_1 = __VLS_asFunctionalComponent(__VLS_0, new __VLS_0({
        size: "24px",
    }));
    const __VLS_2 = __VLS_1({
        size: "24px",
    }, ...__VLS_functionalComponentArgsRest(__VLS_1));
    __VLS_3.slots.default;
    var __VLS_3;
}
else if (!__VLS_ctx.items.length) {
    const __VLS_4 = {}.VanEmpty;
    /** @type {[typeof __VLS_components.VanEmpty, typeof __VLS_components.vanEmpty, ]} */ ;
    // @ts-ignore
    const __VLS_5 = __VLS_asFunctionalComponent(__VLS_4, new __VLS_4({
        description: (__VLS_ctx.emptyText),
    }));
    const __VLS_6 = __VLS_5({
        description: (__VLS_ctx.emptyText),
    }, ...__VLS_functionalComponentArgsRest(__VLS_5));
}
else if (__VLS_ctx.useInfiniteScroll) {
    const __VLS_8 = {}.VanList;
    /** @type {[typeof __VLS_components.VanList, typeof __VLS_components.vanList, typeof __VLS_components.VanList, typeof __VLS_components.vanList, ]} */ ;
    // @ts-ignore
    const __VLS_9 = __VLS_asFunctionalComponent(__VLS_8, new __VLS_8({
        ...{ 'onLoad': {} },
        loading: (__VLS_ctx.internalLoading),
        finished: (__VLS_ctx.finished),
        finishedText: "没有更多了",
        immediateCheck: (false),
        scrollContainer: (__VLS_ctx.getScrollContainer),
    }));
    const __VLS_10 = __VLS_9({
        ...{ 'onLoad': {} },
        loading: (__VLS_ctx.internalLoading),
        finished: (__VLS_ctx.finished),
        finishedText: "没有更多了",
        immediateCheck: (false),
        scrollContainer: (__VLS_ctx.getScrollContainer),
    }, ...__VLS_functionalComponentArgsRest(__VLS_9));
    let __VLS_12;
    let __VLS_13;
    let __VLS_14;
    const __VLS_15 = {
        onLoad: (__VLS_ctx.onLoadMore)
    };
    __VLS_11.slots.default;
    for (const [item] of __VLS_getVForSourceType((__VLS_ctx.items))) {
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ onClick: (...[$event]) => {
                    if (!!(__VLS_ctx.loading && !__VLS_ctx.items.length))
                        return;
                    if (!!(!__VLS_ctx.items.length))
                        return;
                    if (!(__VLS_ctx.useInfiniteScroll))
                        return;
                    __VLS_ctx.isExpired(item) ? null : __VLS_ctx.$emit('card-click', item);
                } },
            key: (item.id),
            ...{ class: "activity-card animate__animated animate__fadeInUp" },
            ...{ class: ({ 'expired': __VLS_ctx.isExpired(item) }) },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "card-image-container" },
        });
        if (__VLS_ctx.getActivityStatus(item)) {
            const __VLS_16 = {}.VanTag;
            /** @type {[typeof __VLS_components.VanTag, typeof __VLS_components.vanTag, typeof __VLS_components.VanTag, typeof __VLS_components.vanTag, ]} */ ;
            // @ts-ignore
            const __VLS_17 = __VLS_asFunctionalComponent(__VLS_16, new __VLS_16({
                type: (__VLS_ctx.getStatusTagType(item)),
                size: "medium",
                ...{ class: "activity-status-tag" },
                round: true,
            }));
            const __VLS_18 = __VLS_17({
                type: (__VLS_ctx.getStatusTagType(item)),
                size: "medium",
                ...{ class: "activity-status-tag" },
                round: true,
            }, ...__VLS_functionalComponentArgsRest(__VLS_17));
            __VLS_19.slots.default;
            (__VLS_ctx.getActivityStatus(item));
            var __VLS_19;
        }
        if (item.activity_type || item.cat_name) {
            const __VLS_20 = {}.VanTag;
            /** @type {[typeof __VLS_components.VanTag, typeof __VLS_components.vanTag, typeof __VLS_components.VanTag, typeof __VLS_components.vanTag, ]} */ ;
            // @ts-ignore
            const __VLS_21 = __VLS_asFunctionalComponent(__VLS_20, new __VLS_20({
                type: "primary",
                size: "medium",
                ...{ class: "activity-type-tag" },
                round: true,
            }));
            const __VLS_22 = __VLS_21({
                type: "primary",
                size: "medium",
                ...{ class: "activity-type-tag" },
                round: true,
            }, ...__VLS_functionalComponentArgsRest(__VLS_21));
            __VLS_23.slots.default;
            (item.activity_type || item.cat_name || '活动');
            var __VLS_23;
        }
        __VLS_asFunctionalElement(__VLS_intrinsicElements.img)({
            src: (item.thumbnail),
            alt: (item.name),
            ...{ class: "activity-image" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "image-overlay" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "card-content" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.h3, __VLS_intrinsicElements.h3)({
            ...{ class: "activity-title" },
        });
        (item.name);
        __VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({
            ...{ class: "activity-description" },
        });
        (item.desc || item.description);
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "activity-details" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-item" },
            ...{ style: (__VLS_ctx.getHighlightStyle('time')) },
        });
        const __VLS_24 = {}.VanIcon;
        /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
        // @ts-ignore
        const __VLS_25 = __VLS_asFunctionalComponent(__VLS_24, new __VLS_24({
            name: "clock-o",
            ...{ style: ({ color: __VLS_ctx.getHighlightStyle('time').color }) },
        }));
        const __VLS_26 = __VLS_25({
            name: "clock-o",
            ...{ style: ({ color: __VLS_ctx.getHighlightStyle('time').color }) },
        }, ...__VLS_functionalComponentArgsRest(__VLS_25));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-content" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-label" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-value" },
            ...{ style: ({ color: __VLS_ctx.getHighlightStyle('time').color }) },
        });
        (__VLS_ctx.formatActivityTime(item));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-item" },
            ...{ style: (__VLS_ctx.getHighlightStyle('location')) },
        });
        const __VLS_28 = {}.VanIcon;
        /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
        // @ts-ignore
        const __VLS_29 = __VLS_asFunctionalComponent(__VLS_28, new __VLS_28({
            name: "location-o",
            ...{ style: ({ color: __VLS_ctx.getHighlightStyle('location').color }) },
        }));
        const __VLS_30 = __VLS_29({
            name: "location-o",
            ...{ style: ({ color: __VLS_ctx.getHighlightStyle('location').color }) },
        }, ...__VLS_functionalComponentArgsRest(__VLS_29));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-content" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-label" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-value" },
            ...{ style: ({ color: __VLS_ctx.getHighlightStyle('location').color }) },
        });
        (__VLS_ctx.formatActivityLocation(item));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-item" },
            ...{ style: (__VLS_ctx.getHighlightStyle('participants')) },
        });
        const __VLS_32 = {}.VanIcon;
        /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
        // @ts-ignore
        const __VLS_33 = __VLS_asFunctionalComponent(__VLS_32, new __VLS_32({
            name: "friends-o",
            ...{ style: ({ color: __VLS_ctx.getHighlightStyle('participants').color }) },
        }));
        const __VLS_34 = __VLS_33({
            name: "friends-o",
            ...{ style: ({ color: __VLS_ctx.getHighlightStyle('participants').color }) },
        }, ...__VLS_functionalComponentArgsRest(__VLS_33));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-content" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-label" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-value" },
            ...{ style: ({ color: __VLS_ctx.getHighlightStyle('participants').color }) },
        });
        (__VLS_ctx.formatParticipantCount(item));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-item" },
            ...{ style: (__VLS_ctx.getHighlightStyle('form')) },
        });
        const __VLS_36 = {}.VanIcon;
        /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
        // @ts-ignore
        const __VLS_37 = __VLS_asFunctionalComponent(__VLS_36, new __VLS_36({
            name: (__VLS_ctx.getActivityFormIcon(item)),
            ...{ style: ({ color: __VLS_ctx.getHighlightStyle('form').color }) },
        }));
        const __VLS_38 = __VLS_37({
            name: (__VLS_ctx.getActivityFormIcon(item)),
            ...{ style: ({ color: __VLS_ctx.getHighlightStyle('form').color }) },
        }, ...__VLS_functionalComponentArgsRest(__VLS_37));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-content" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-label" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-value activity-form" },
            ...{ class: (__VLS_ctx.getActivityFormClass(item)) },
            ...{ style: ({ color: __VLS_ctx.getHighlightStyle('form').color }) },
        });
        (__VLS_ctx.formatActivityForm(item));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-item" },
            ...{ style: (__VLS_ctx.getHighlightStyle('deadline')) },
        });
        const __VLS_40 = {}.VanIcon;
        /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
        // @ts-ignore
        const __VLS_41 = __VLS_asFunctionalComponent(__VLS_40, new __VLS_40({
            name: "calendar-o",
            ...{ style: ({ color: __VLS_ctx.getHighlightStyle('deadline').color }) },
        }));
        const __VLS_42 = __VLS_41({
            name: "calendar-o",
            ...{ style: ({ color: __VLS_ctx.getHighlightStyle('deadline').color }) },
        }, ...__VLS_functionalComponentArgsRest(__VLS_41));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-content" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-label" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-value" },
            ...{ style: ({ color: __VLS_ctx.getHighlightStyle('deadline').color }) },
        });
        (__VLS_ctx.formatRegistrationDeadline(item));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-item" },
            ...{ style: (__VLS_ctx.getHighlightStyle('contact')) },
        });
        const __VLS_44 = {}.VanIcon;
        /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
        // @ts-ignore
        const __VLS_45 = __VLS_asFunctionalComponent(__VLS_44, new __VLS_44({
            name: "phone-o",
            ...{ style: ({ color: __VLS_ctx.getHighlightStyle('contact').color }) },
        }));
        const __VLS_46 = __VLS_45({
            name: "phone-o",
            ...{ style: ({ color: __VLS_ctx.getHighlightStyle('contact').color }) },
        }, ...__VLS_functionalComponentArgsRest(__VLS_45));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-content" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-label" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-value" },
            ...{ style: ({ color: __VLS_ctx.getHighlightStyle('contact').color }) },
        });
        (__VLS_ctx.formatContact(item));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "activity-footer" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "footer-left" },
        });
        if (item.tags && item.tags.length) {
            __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
                ...{ class: "activity-tags" },
            });
            for (const [tag, index] of __VLS_getVForSourceType((item.tags))) {
                __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
                    key: (index),
                    ...{ class: "activity-tag" },
                });
                (tag);
            }
        }
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "footer-right" },
        });
        if (item.views || item.likes) {
            __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
                ...{ class: "activity-heat" },
            });
            const __VLS_48 = {}.VanIcon;
            /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
            // @ts-ignore
            const __VLS_49 = __VLS_asFunctionalComponent(__VLS_48, new __VLS_48({
                name: "fire-o",
            }));
            const __VLS_50 = __VLS_49({
                name: "fire-o",
            }, ...__VLS_functionalComponentArgsRest(__VLS_49));
            __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({});
            (item.views || item.likes || 0);
        }
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "activity-actions" },
        });
        const __VLS_52 = {}.VanButton;
        /** @type {[typeof __VLS_components.VanButton, typeof __VLS_components.vanButton, typeof __VLS_components.VanButton, typeof __VLS_components.vanButton, ]} */ ;
        // @ts-ignore
        const __VLS_53 = __VLS_asFunctionalComponent(__VLS_52, new __VLS_52({
            ...{ 'onClick': {} },
            type: "default",
            size: "small",
            icon: "share-o",
            ...{ class: "action-button share-button" },
            disabled: (!__VLS_ctx.isActivityDataComplete(item) || __VLS_ctx.isExpired(item)),
        }));
        const __VLS_54 = __VLS_53({
            ...{ 'onClick': {} },
            type: "default",
            size: "small",
            icon: "share-o",
            ...{ class: "action-button share-button" },
            disabled: (!__VLS_ctx.isActivityDataComplete(item) || __VLS_ctx.isExpired(item)),
        }, ...__VLS_functionalComponentArgsRest(__VLS_53));
        let __VLS_56;
        let __VLS_57;
        let __VLS_58;
        const __VLS_59 = {
            onClick: (...[$event]) => {
                if (!!(__VLS_ctx.loading && !__VLS_ctx.items.length))
                    return;
                if (!!(!__VLS_ctx.items.length))
                    return;
                if (!(__VLS_ctx.useInfiniteScroll))
                    return;
                __VLS_ctx.handleShare(item);
            }
        };
        __VLS_55.slots.default;
        var __VLS_55;
        const __VLS_60 = {}.VanButton;
        /** @type {[typeof __VLS_components.VanButton, typeof __VLS_components.vanButton, typeof __VLS_components.VanButton, typeof __VLS_components.vanButton, ]} */ ;
        // @ts-ignore
        const __VLS_61 = __VLS_asFunctionalComponent(__VLS_60, new __VLS_60({
            ...{ 'onClick': {} },
            type: (__VLS_ctx.getRegisterButtonType(item)),
            size: "small",
            icon: (__VLS_ctx.getRegisterButtonIcon(item)),
            ...{ class: "action-button register-button" },
            disabled: (!__VLS_ctx.canRegister(item)),
        }));
        const __VLS_62 = __VLS_61({
            ...{ 'onClick': {} },
            type: (__VLS_ctx.getRegisterButtonType(item)),
            size: "small",
            icon: (__VLS_ctx.getRegisterButtonIcon(item)),
            ...{ class: "action-button register-button" },
            disabled: (!__VLS_ctx.canRegister(item)),
        }, ...__VLS_functionalComponentArgsRest(__VLS_61));
        let __VLS_64;
        let __VLS_65;
        let __VLS_66;
        const __VLS_67 = {
            onClick: (...[$event]) => {
                if (!!(__VLS_ctx.loading && !__VLS_ctx.items.length))
                    return;
                if (!!(!__VLS_ctx.items.length))
                    return;
                if (!(__VLS_ctx.useInfiniteScroll))
                    return;
                __VLS_ctx.handleRegister(item);
            }
        };
        __VLS_63.slots.default;
        (__VLS_ctx.getRegisterButtonText(item));
        var __VLS_63;
    }
    var __VLS_11;
}
else {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({});
    for (const [item] of __VLS_getVForSourceType((__VLS_ctx.items))) {
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ onClick: (...[$event]) => {
                    if (!!(__VLS_ctx.loading && !__VLS_ctx.items.length))
                        return;
                    if (!!(!__VLS_ctx.items.length))
                        return;
                    if (!!(__VLS_ctx.useInfiniteScroll))
                        return;
                    __VLS_ctx.isExpired(item) ? null : __VLS_ctx.$emit('card-click', item);
                } },
            key: (item.id),
            ...{ class: "activity-card animate__animated animate__fadeInUp" },
            ...{ class: ({ 'expired': __VLS_ctx.isExpired(item) }) },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "card-image-container" },
        });
        if (__VLS_ctx.getActivityStatus(item)) {
            const __VLS_68 = {}.VanTag;
            /** @type {[typeof __VLS_components.VanTag, typeof __VLS_components.vanTag, typeof __VLS_components.VanTag, typeof __VLS_components.vanTag, ]} */ ;
            // @ts-ignore
            const __VLS_69 = __VLS_asFunctionalComponent(__VLS_68, new __VLS_68({
                type: (__VLS_ctx.getStatusTagType(item)),
                size: "medium",
                ...{ class: "activity-status-tag" },
                round: true,
            }));
            const __VLS_70 = __VLS_69({
                type: (__VLS_ctx.getStatusTagType(item)),
                size: "medium",
                ...{ class: "activity-status-tag" },
                round: true,
            }, ...__VLS_functionalComponentArgsRest(__VLS_69));
            __VLS_71.slots.default;
            (__VLS_ctx.getActivityStatus(item));
            var __VLS_71;
        }
        if (item.activity_type || item.cat_name) {
            const __VLS_72 = {}.VanTag;
            /** @type {[typeof __VLS_components.VanTag, typeof __VLS_components.vanTag, typeof __VLS_components.VanTag, typeof __VLS_components.vanTag, ]} */ ;
            // @ts-ignore
            const __VLS_73 = __VLS_asFunctionalComponent(__VLS_72, new __VLS_72({
                type: "primary",
                size: "medium",
                ...{ class: "activity-type-tag" },
                round: true,
            }));
            const __VLS_74 = __VLS_73({
                type: "primary",
                size: "medium",
                ...{ class: "activity-type-tag" },
                round: true,
            }, ...__VLS_functionalComponentArgsRest(__VLS_73));
            __VLS_75.slots.default;
            (item.activity_type || item.cat_name || '活动');
            var __VLS_75;
        }
        __VLS_asFunctionalElement(__VLS_intrinsicElements.img)({
            src: (item.thumbnail),
            alt: (item.name),
            ...{ class: "activity-image" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "image-overlay" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "card-content" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.h3, __VLS_intrinsicElements.h3)({
            ...{ class: "activity-title" },
        });
        (item.name);
        __VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({
            ...{ class: "activity-description" },
        });
        (item.desc || item.description);
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "activity-details" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-item" },
            ...{ style: (__VLS_ctx.getHighlightStyle('time')) },
        });
        const __VLS_76 = {}.VanIcon;
        /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
        // @ts-ignore
        const __VLS_77 = __VLS_asFunctionalComponent(__VLS_76, new __VLS_76({
            name: "clock-o",
            ...{ style: ({ color: __VLS_ctx.getHighlightStyle('time').color }) },
        }));
        const __VLS_78 = __VLS_77({
            name: "clock-o",
            ...{ style: ({ color: __VLS_ctx.getHighlightStyle('time').color }) },
        }, ...__VLS_functionalComponentArgsRest(__VLS_77));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-content" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-label" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-value" },
            ...{ style: ({ color: __VLS_ctx.getHighlightStyle('time').color }) },
        });
        (__VLS_ctx.formatActivityTime(item));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-item" },
            ...{ style: (__VLS_ctx.getHighlightStyle('location')) },
        });
        const __VLS_80 = {}.VanIcon;
        /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
        // @ts-ignore
        const __VLS_81 = __VLS_asFunctionalComponent(__VLS_80, new __VLS_80({
            name: "location-o",
            ...{ style: ({ color: __VLS_ctx.getHighlightStyle('location').color }) },
        }));
        const __VLS_82 = __VLS_81({
            name: "location-o",
            ...{ style: ({ color: __VLS_ctx.getHighlightStyle('location').color }) },
        }, ...__VLS_functionalComponentArgsRest(__VLS_81));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-content" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-label" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-value" },
            ...{ style: ({ color: __VLS_ctx.getHighlightStyle('location').color }) },
        });
        (__VLS_ctx.formatActivityLocation(item));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-item" },
            ...{ style: (__VLS_ctx.getHighlightStyle('participants')) },
        });
        const __VLS_84 = {}.VanIcon;
        /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
        // @ts-ignore
        const __VLS_85 = __VLS_asFunctionalComponent(__VLS_84, new __VLS_84({
            name: "friends-o",
            ...{ style: ({ color: __VLS_ctx.getHighlightStyle('participants').color }) },
        }));
        const __VLS_86 = __VLS_85({
            name: "friends-o",
            ...{ style: ({ color: __VLS_ctx.getHighlightStyle('participants').color }) },
        }, ...__VLS_functionalComponentArgsRest(__VLS_85));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-content" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-label" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-value" },
            ...{ style: ({ color: __VLS_ctx.getHighlightStyle('participants').color }) },
        });
        (__VLS_ctx.formatParticipantCount(item));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-item" },
            ...{ style: (__VLS_ctx.getHighlightStyle('form')) },
        });
        const __VLS_88 = {}.VanIcon;
        /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
        // @ts-ignore
        const __VLS_89 = __VLS_asFunctionalComponent(__VLS_88, new __VLS_88({
            name: (__VLS_ctx.getActivityFormIcon(item)),
            ...{ style: ({ color: __VLS_ctx.getHighlightStyle('form').color }) },
        }));
        const __VLS_90 = __VLS_89({
            name: (__VLS_ctx.getActivityFormIcon(item)),
            ...{ style: ({ color: __VLS_ctx.getHighlightStyle('form').color }) },
        }, ...__VLS_functionalComponentArgsRest(__VLS_89));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-content" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-label" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-value activity-form" },
            ...{ class: (__VLS_ctx.getActivityFormClass(item)) },
            ...{ style: ({ color: __VLS_ctx.getHighlightStyle('form').color }) },
        });
        (__VLS_ctx.formatActivityForm(item));
        if (false) {
            __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
                ...{ class: "detail-item" },
                ...{ style: (__VLS_ctx.getHighlightStyle('category')) },
            });
            const __VLS_92 = {}.VanIcon;
            /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
            // @ts-ignore
            const __VLS_93 = __VLS_asFunctionalComponent(__VLS_92, new __VLS_92({
                name: "cluster-o",
                ...{ style: ({ color: __VLS_ctx.getHighlightStyle('category').color }) },
            }));
            const __VLS_94 = __VLS_93({
                name: "cluster-o",
                ...{ style: ({ color: __VLS_ctx.getHighlightStyle('category').color }) },
            }, ...__VLS_functionalComponentArgsRest(__VLS_93));
            __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
                ...{ class: "detail-content" },
            });
            __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
                ...{ class: "detail-label" },
            });
            __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
                ...{ class: "detail-value" },
                ...{ style: ({ color: __VLS_ctx.getHighlightStyle('category').color }) },
            });
            (item.cat_name);
        }
        if (false) {
            __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
                ...{ class: "detail-item" },
                ...{ style: (__VLS_ctx.getHighlightStyle('fee')) },
            });
            const __VLS_96 = {}.VanIcon;
            /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
            // @ts-ignore
            const __VLS_97 = __VLS_asFunctionalComponent(__VLS_96, new __VLS_96({
                name: "gold-coin-o",
                ...{ style: ({ color: __VLS_ctx.getHighlightStyle('fee').color }) },
            }));
            const __VLS_98 = __VLS_97({
                name: "gold-coin-o",
                ...{ style: ({ color: __VLS_ctx.getHighlightStyle('fee').color }) },
            }, ...__VLS_functionalComponentArgsRest(__VLS_97));
            __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
                ...{ class: "detail-content" },
            });
            __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
                ...{ class: "detail-label" },
            });
            __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
                ...{ class: "detail-value" },
                ...{ class: ({ 'free-activity': item.isfree === '免费' || item.price === 0 }) },
                ...{ style: ({ color: __VLS_ctx.getHighlightStyle('fee').color }) },
            });
            (__VLS_ctx.formatActivityFee(item));
        }
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-item" },
            ...{ style: (__VLS_ctx.getHighlightStyle('deadline')) },
        });
        const __VLS_100 = {}.VanIcon;
        /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
        // @ts-ignore
        const __VLS_101 = __VLS_asFunctionalComponent(__VLS_100, new __VLS_100({
            name: "calendar-o",
            ...{ style: ({ color: __VLS_ctx.getHighlightStyle('deadline').color }) },
        }));
        const __VLS_102 = __VLS_101({
            name: "calendar-o",
            ...{ style: ({ color: __VLS_ctx.getHighlightStyle('deadline').color }) },
        }, ...__VLS_functionalComponentArgsRest(__VLS_101));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-content" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-label" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-value" },
            ...{ style: ({ color: __VLS_ctx.getHighlightStyle('deadline').color }) },
        });
        (__VLS_ctx.formatRegistrationDeadline(item));
        if (false) {
            __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
                ...{ class: "detail-item" },
                ...{ style: (__VLS_ctx.getHighlightStyle('organizer')) },
            });
            const __VLS_104 = {}.VanIcon;
            /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
            // @ts-ignore
            const __VLS_105 = __VLS_asFunctionalComponent(__VLS_104, new __VLS_104({
                name: "manager-o",
                ...{ style: ({ color: __VLS_ctx.getHighlightStyle('organizer').color }) },
            }));
            const __VLS_106 = __VLS_105({
                name: "manager-o",
                ...{ style: ({ color: __VLS_ctx.getHighlightStyle('organizer').color }) },
            }, ...__VLS_functionalComponentArgsRest(__VLS_105));
            __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
                ...{ class: "detail-content" },
            });
            __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
                ...{ class: "detail-label" },
            });
            __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
                ...{ class: "detail-value" },
                ...{ style: ({ color: __VLS_ctx.getHighlightStyle('organizer').color }) },
            });
            (__VLS_ctx.formatOrganizer(item));
        }
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-item" },
            ...{ style: (__VLS_ctx.getHighlightStyle('contact')) },
        });
        const __VLS_108 = {}.VanIcon;
        /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
        // @ts-ignore
        const __VLS_109 = __VLS_asFunctionalComponent(__VLS_108, new __VLS_108({
            name: "phone-o",
            ...{ style: ({ color: __VLS_ctx.getHighlightStyle('contact').color }) },
        }));
        const __VLS_110 = __VLS_109({
            name: "phone-o",
            ...{ style: ({ color: __VLS_ctx.getHighlightStyle('contact').color }) },
        }, ...__VLS_functionalComponentArgsRest(__VLS_109));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-content" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-label" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-value" },
            ...{ style: ({ color: __VLS_ctx.getHighlightStyle('contact').color }) },
        });
        (__VLS_ctx.formatContact(item));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "activity-footer" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "footer-left" },
        });
        if (item.tags && item.tags.length) {
            __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
                ...{ class: "activity-tags" },
            });
            for (const [tag, index] of __VLS_getVForSourceType((item.tags))) {
                __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
                    key: (index),
                    ...{ class: "activity-tag" },
                });
                (tag);
            }
        }
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "footer-right" },
        });
        if (item.views || item.likes) {
            __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
                ...{ class: "activity-heat" },
            });
            const __VLS_112 = {}.VanIcon;
            /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
            // @ts-ignore
            const __VLS_113 = __VLS_asFunctionalComponent(__VLS_112, new __VLS_112({
                name: "fire-o",
            }));
            const __VLS_114 = __VLS_113({
                name: "fire-o",
            }, ...__VLS_functionalComponentArgsRest(__VLS_113));
            __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({});
            (item.views || item.likes || 0);
        }
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "activity-actions" },
        });
        const __VLS_116 = {}.VanButton;
        /** @type {[typeof __VLS_components.VanButton, typeof __VLS_components.vanButton, typeof __VLS_components.VanButton, typeof __VLS_components.vanButton, ]} */ ;
        // @ts-ignore
        const __VLS_117 = __VLS_asFunctionalComponent(__VLS_116, new __VLS_116({
            ...{ 'onClick': {} },
            type: "default",
            size: "small",
            icon: "share-o",
            ...{ class: "action-button share-button" },
            disabled: (!__VLS_ctx.isActivityDataComplete(item) || __VLS_ctx.isExpired(item)),
        }));
        const __VLS_118 = __VLS_117({
            ...{ 'onClick': {} },
            type: "default",
            size: "small",
            icon: "share-o",
            ...{ class: "action-button share-button" },
            disabled: (!__VLS_ctx.isActivityDataComplete(item) || __VLS_ctx.isExpired(item)),
        }, ...__VLS_functionalComponentArgsRest(__VLS_117));
        let __VLS_120;
        let __VLS_121;
        let __VLS_122;
        const __VLS_123 = {
            onClick: (...[$event]) => {
                if (!!(__VLS_ctx.loading && !__VLS_ctx.items.length))
                    return;
                if (!!(!__VLS_ctx.items.length))
                    return;
                if (!!(__VLS_ctx.useInfiniteScroll))
                    return;
                __VLS_ctx.handleShare(item);
            }
        };
        __VLS_119.slots.default;
        var __VLS_119;
        const __VLS_124 = {}.VanButton;
        /** @type {[typeof __VLS_components.VanButton, typeof __VLS_components.vanButton, typeof __VLS_components.VanButton, typeof __VLS_components.vanButton, ]} */ ;
        // @ts-ignore
        const __VLS_125 = __VLS_asFunctionalComponent(__VLS_124, new __VLS_124({
            ...{ 'onClick': {} },
            type: (__VLS_ctx.getRegisterButtonType(item)),
            size: "small",
            icon: (__VLS_ctx.getRegisterButtonIcon(item)),
            ...{ class: "action-button register-button" },
            disabled: (!__VLS_ctx.canRegister(item)),
        }));
        const __VLS_126 = __VLS_125({
            ...{ 'onClick': {} },
            type: (__VLS_ctx.getRegisterButtonType(item)),
            size: "small",
            icon: (__VLS_ctx.getRegisterButtonIcon(item)),
            ...{ class: "action-button register-button" },
            disabled: (!__VLS_ctx.canRegister(item)),
        }, ...__VLS_functionalComponentArgsRest(__VLS_125));
        let __VLS_128;
        let __VLS_129;
        let __VLS_130;
        const __VLS_131 = {
            onClick: (...[$event]) => {
                if (!!(__VLS_ctx.loading && !__VLS_ctx.items.length))
                    return;
                if (!!(!__VLS_ctx.items.length))
                    return;
                if (!!(__VLS_ctx.useInfiniteScroll))
                    return;
                __VLS_ctx.handleRegister(item);
            }
        };
        __VLS_127.slots.default;
        (__VLS_ctx.getRegisterButtonText(item));
        var __VLS_127;
    }
}
/** @type {__VLS_StyleScopedClasses['activity-common-card-list']} */ ;
/** @type {__VLS_StyleScopedClasses['loading-container']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-card']} */ ;
/** @type {__VLS_StyleScopedClasses['animate__animated']} */ ;
/** @type {__VLS_StyleScopedClasses['animate__fadeInUp']} */ ;
/** @type {__VLS_StyleScopedClasses['expired']} */ ;
/** @type {__VLS_StyleScopedClasses['card-image-container']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-status-tag']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-type-tag']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-image']} */ ;
/** @type {__VLS_StyleScopedClasses['image-overlay']} */ ;
/** @type {__VLS_StyleScopedClasses['card-content']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-title']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-description']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-details']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-content']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-label']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-value']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-content']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-label']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-value']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-content']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-label']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-value']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-content']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-label']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-value']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-form']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-content']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-label']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-value']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-content']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-label']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-value']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-footer']} */ ;
/** @type {__VLS_StyleScopedClasses['footer-left']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-tags']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-tag']} */ ;
/** @type {__VLS_StyleScopedClasses['footer-right']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-heat']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-actions']} */ ;
/** @type {__VLS_StyleScopedClasses['action-button']} */ ;
/** @type {__VLS_StyleScopedClasses['share-button']} */ ;
/** @type {__VLS_StyleScopedClasses['action-button']} */ ;
/** @type {__VLS_StyleScopedClasses['register-button']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-card']} */ ;
/** @type {__VLS_StyleScopedClasses['animate__animated']} */ ;
/** @type {__VLS_StyleScopedClasses['animate__fadeInUp']} */ ;
/** @type {__VLS_StyleScopedClasses['expired']} */ ;
/** @type {__VLS_StyleScopedClasses['card-image-container']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-status-tag']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-type-tag']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-image']} */ ;
/** @type {__VLS_StyleScopedClasses['image-overlay']} */ ;
/** @type {__VLS_StyleScopedClasses['card-content']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-title']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-description']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-details']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-content']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-label']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-value']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-content']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-label']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-value']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-content']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-label']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-value']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-content']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-label']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-value']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-form']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-content']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-label']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-value']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-content']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-label']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-value']} */ ;
/** @type {__VLS_StyleScopedClasses['free-activity']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-content']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-label']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-value']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-content']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-label']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-value']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-content']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-label']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-value']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-footer']} */ ;
/** @type {__VLS_StyleScopedClasses['footer-left']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-tags']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-tag']} */ ;
/** @type {__VLS_StyleScopedClasses['footer-right']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-heat']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-actions']} */ ;
/** @type {__VLS_StyleScopedClasses['action-button']} */ ;
/** @type {__VLS_StyleScopedClasses['share-button']} */ ;
/** @type {__VLS_StyleScopedClasses['action-button']} */ ;
/** @type {__VLS_StyleScopedClasses['register-button']} */ ;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            scrollContainerRef: scrollContainerRef,
            getScrollContainer: getScrollContainer,
            internalLoading: internalLoading,
            onLoadMore: onLoadMore,
            getActivityStatus: getActivityStatus,
            getStatusTagType: getStatusTagType,
            formatActivityTime: formatActivityTime,
            formatParticipantCount: formatParticipantCount,
            formatActivityFee: formatActivityFee,
            getActivityFormIcon: getActivityFormIcon,
            getActivityFormClass: getActivityFormClass,
            formatActivityForm: formatActivityForm,
            isExpired: isExpired,
            isActivityDataComplete: isActivityDataComplete,
            handleShare: handleShare,
            handleRegister: handleRegister,
            canRegister: canRegister,
            getRegisterButtonType: getRegisterButtonType,
            getRegisterButtonIcon: getRegisterButtonIcon,
            getRegisterButtonText: getRegisterButtonText,
            formatActivityLocation: formatActivityLocation,
            formatRegistrationDeadline: formatRegistrationDeadline,
            formatOrganizer: formatOrganizer,
            formatContact: formatContact,
            getHighlightStyle: getHighlightStyle,
        };
    },
    emits: {},
    __typeProps: {},
    props: {},
});
export default (await import('vue')).defineComponent({
    setup() {
        return {};
    },
    emits: {},
    __typeProps: {},
    props: {},
});
; /* PartiallyEnd: #4569/main.vue */
