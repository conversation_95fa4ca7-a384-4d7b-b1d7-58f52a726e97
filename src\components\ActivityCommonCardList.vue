<template>
  <div class="activity-common-card-list" ref="scrollContainerRef">
    <!-- 首次加载中 -->
    <div v-if="loading && !items.length" class="loading-container">
      <van-loading size="24px">加载中...</van-loading>
    </div>

    <!-- 空状态 -->
    <van-empty v-else-if="!items.length" :description="emptyText" />

    <!-- 列表 -->
    <van-list v-else-if="useInfiniteScroll" v-model:loading="internalLoading" :finished="finished" finished-text="没有更多了"
      @load="onLoadMore" :immediate-check="false" :scroll-container="getScrollContainer">
      <div v-for="item in items" :key="item.id" class="activity-card animate__animated animate__fadeInUp"
        :class="{ 'expired': isExpired(item) }"
        @click="isExpired(item) ? null : $emit('card-click', item)">
        
        <!-- 活动图片容器 -->
        <div class="card-image-container">
          <!-- 活动状态标签 -->
          <van-tag v-if="getActivityStatus(item)" :type="getStatusTagType(item)" size="medium" class="activity-status-tag" round>
            {{ getActivityStatus(item) }}
          </van-tag>
          <!-- 活动类型标签 -->
          <van-tag v-if="item.activity_type || item.cat_name" type="primary" size="medium" class="activity-type-tag" round>
            {{ item.activity_type || item.cat_name || '活动' }}
          </van-tag>
          <img :src="item.thumbnail" :alt="item.name" class="activity-image" />
          <div class="image-overlay"></div>
        </div>

        <!-- 活动内容 -->
        <div class="card-content">
          <h3 class="activity-title">{{ item.name }}</h3>
          <p class="activity-description">{{ item.desc || item.description }}</p>
          
          <!-- 活动详细信息 -->
          <div class="activity-details">
            <!-- 活动时间 -->
            <div class="detail-item" :style="getHighlightStyle('time')">
              <van-icon name="clock-o" :style="{ color: getHighlightStyle('time').color }" />
              <div class="detail-content">
                <span class="detail-label">活动时间：：</span>
                <span class="detail-value" :style="{ color: getHighlightStyle('time').color }">
                  {{ formatActivityTime(item) }}
                </span>
              </div>
            </div>

            <!-- 活动地点 -->
            <div class="detail-item" :style="getHighlightStyle('location')">
              <van-icon name="location-o" :style="{ color: getHighlightStyle('location').color }" />
              <div class="detail-content">
                <span class="detail-label">活动地点：</span>
                <span class="detail-value" :style="{ color: getHighlightStyle('location').color }">{{ formatActivityLocation(item) }}</span>
              </div>
            </div>

            <!-- 活动人数 -->
            <div class="detail-item" :style="getHighlightStyle('participants')">
              <van-icon name="friends-o" :style="{ color: getHighlightStyle('participants').color }" />
              <div class="detail-content">
                <span class="detail-label">参与人数：</span>
                <span class="detail-value" :style="{ color: getHighlightStyle('participants').color }">
                  {{ formatParticipantCount(item) }}
                </span>
              </div>
            </div>

            <!-- 活动形式 -->
            <div class="detail-item" :style="getHighlightStyle('form')">
              <van-icon :name="getActivityFormIcon(item)" :style="{ color: getHighlightStyle('form').color }" />
              <div class="detail-content">
                <span class="detail-label">活动形式：</span>
                <span class="detail-value activity-form" :class="getActivityFormClass(item)" :style="{ color: getHighlightStyle('form').color }">
                  {{ formatActivityForm(item) }}
                </span>
              </div>
            </div>


            <!-- 报名截止时间 -->
            <div class="detail-item" :style="getHighlightStyle('deadline')">
              <van-icon name="calendar-o" :style="{ color: getHighlightStyle('deadline').color }" />
              <div class="detail-content">
                <span class="detail-label">报名截止：</span>
                <span class="detail-value" :style="{ color: getHighlightStyle('deadline').color }">{{ formatRegistrationDeadline(item) }}</span>
              </div>
            </div>


            <!-- 联系方式 -->
            <div class="detail-item" :style="getHighlightStyle('contact')">
              <van-icon name="phone-o" :style="{ color: getHighlightStyle('contact').color }" />
              <div class="detail-content">
                <span class="detail-label">联系方式：</span>
                <span class="detail-value" :style="{ color: getHighlightStyle('contact').color }">{{ formatContact(item) }}</span>
              </div>
            </div>
          </div>

          <!-- 活动标签和状态 -->
          <div class="activity-footer">
            <div class="footer-left">
              <div class="activity-tags" v-if="item.tags && item.tags.length">
                <span v-for="(tag, index) in item.tags" :key="index" class="activity-tag">{{ tag }}</span>
              </div>
            </div>
            <div class="footer-right">
              <!-- 活动热度 -->
              <div class="activity-heat" v-if="item.views || item.likes">
                <van-icon name="fire-o" />
                <span>{{ item.views || item.likes || 0 }}</span>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="activity-actions">
            <van-button 
              type="default" 
              size="small" 
              icon="share-o" 
              class="action-button share-button"
              :disabled="!isActivityDataComplete(item) || isExpired(item)"
              @click.stop="handleShare(item)"
            >
              转发
            </van-button>
            <van-button 
              :type="getRegisterButtonType(item)" 
              size="small" 
              :icon="getRegisterButtonIcon(item)"
              class="action-button register-button"
              :disabled="!canRegister(item)"
              @click.stop="handleRegister(item)"
            >
              {{ getRegisterButtonText(item) }}
            </van-button>
          </div>
        </div>
      </div>
    </van-list>

    <!-- 非无限滚动模式 -->
    <div v-else>
      <div v-for="item in items" :key="item.id" class="activity-card animate__animated animate__fadeInUp"
        :class="{ 'expired': isExpired(item) }"
        @click="isExpired(item) ? null : $emit('card-click', item)">
        
        <!-- 活动图片容器 -->
        <div class="card-image-container">
          <!-- 活动状态标签 -->
          <van-tag v-if="getActivityStatus(item)" :type="getStatusTagType(item)" size="medium" class="activity-status-tag" round>
            {{ getActivityStatus(item) }}
          </van-tag>
          <!-- 活动类型标签 -->
          <van-tag v-if="item.activity_type || item.cat_name" type="primary" size="medium" class="activity-type-tag" round>
            {{ item.activity_type || item.cat_name || '活动' }}
          </van-tag>
          <img :src="item.thumbnail" :alt="item.name" class="activity-image" />
          <div class="image-overlay"></div>
        </div>

        <!-- 活动内容 -->
        <div class="card-content">
          <h3 class="activity-title">{{ item.name }}</h3>
          <p class="activity-description">{{ item.desc || item.description }}</p>
          
          <!-- 活动详细信息 -->
          <div class="activity-details">
            <!-- 活动时间 -->
            <div class="detail-item" :style="getHighlightStyle('time')">
              <van-icon name="clock-o" :style="{ color: getHighlightStyle('time').color }" />
              <div class="detail-content">
                <span class="detail-label">活动时间</span>
                <span class="detail-value" :style="{ color: getHighlightStyle('time').color }">
                  {{ formatActivityTime(item) }}
                </span>
              </div>
            </div>

            <!-- 活动地点 -->
            <div class="detail-item" :style="getHighlightStyle('location')">
              <van-icon name="location-o" :style="{ color: getHighlightStyle('location').color }" />
              <div class="detail-content">
                <span class="detail-label">活动地点</span>
                <span class="detail-value" :style="{ color: getHighlightStyle('location').color }">{{ formatActivityLocation(item) }}</span>
              </div>
            </div>

            <!-- 活动人数 -->
            <div class="detail-item" :style="getHighlightStyle('participants')">
              <van-icon name="friends-o" :style="{ color: getHighlightStyle('participants').color }" />
              <div class="detail-content">
                <span class="detail-label">参与人数</span>
                <span class="detail-value" :style="{ color: getHighlightStyle('participants').color }">
                  {{ formatParticipantCount(item) }}
                </span>
              </div>
            </div>

            <!-- 活动形式 -->
            <div class="detail-item" :style="getHighlightStyle('form')">
              <van-icon :name="getActivityFormIcon(item)" :style="{ color: getHighlightStyle('form').color }" />
              <div class="detail-content">
                <span class="detail-label">活动形式</span>
                <span class="detail-value activity-form" :class="getActivityFormClass(item)" :style="{ color: getHighlightStyle('form').color }">
                  {{ formatActivityForm(item) }}
                </span>
              </div>
            </div>

            <!-- 活动分类 -->
            <div class="detail-item" v-if="false" :style="getHighlightStyle('category')">
              <van-icon name="cluster-o" :style="{ color: getHighlightStyle('category').color }" />
              <div class="detail-content">
                <span class="detail-label">活动分类</span>
                <span class="detail-value" :style="{ color: getHighlightStyle('category').color }">{{ item.cat_name }}</span>
              </div>
            </div>

            <!-- 活动费用 -->
            <div class="detail-item" v-if="false" :style="getHighlightStyle('fee')">
              <van-icon name="gold-coin-o" :style="{ color: getHighlightStyle('fee').color }" />
              <div class="detail-content">
                <span class="detail-label">活动费用</span>
                <span class="detail-value" :class="{ 'free-activity': item.isfree === '免费' || item.price === 0 }" :style="{ color: getHighlightStyle('fee').color }">
                  {{ formatActivityFee(item) }}
                </span>
              </div>
            </div>

            <!-- 报名截止时间 -->
            <div class="detail-item" :style="getHighlightStyle('deadline')">
              <van-icon name="calendar-o" :style="{ color: getHighlightStyle('deadline').color }" />
              <div class="detail-content">
                <span class="detail-label">报名截止</span>
                <span class="detail-value" :style="{ color: getHighlightStyle('deadline').color }">{{ formatRegistrationDeadline(item) }}</span>
              </div>
            </div>

            <!-- 活动主办方 -->
            <div class="detail-item" v-if="false" :style="getHighlightStyle('organizer')">
              <van-icon name="manager-o" :style="{ color: getHighlightStyle('organizer').color }" />
              <div class="detail-content">
                <span class="detail-label">主办方</span>
                <span class="detail-value" :style="{ color: getHighlightStyle('organizer').color }">{{ formatOrganizer(item) }}</span>
              </div>
            </div>

            <!-- 联系方式 -->
            <div class="detail-item" :style="getHighlightStyle('contact')">
              <van-icon name="phone-o" :style="{ color: getHighlightStyle('contact').color }" />
              <div class="detail-content">
                <span class="detail-label">联系方式</span>
                <span class="detail-value" :style="{ color: getHighlightStyle('contact').color }">{{ formatContact(item) }}</span>
              </div>
            </div>
          </div>

          <!-- 活动标签和状态 -->
          <div class="activity-footer">
            <div class="footer-left">
              <div class="activity-tags" v-if="item.tags && item.tags.length">
                <span v-for="(tag, index) in item.tags" :key="index" class="activity-tag">{{ tag }}</span>
              </div>
            </div>
            <div class="footer-right">
              <!-- 活动热度 -->
              <div class="activity-heat" v-if="item.views || item.likes">
                <van-icon name="fire-o" />
                <span>{{ item.views || item.likes || 0 }}</span>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="activity-actions">
            <van-button 
              type="default" 
              size="small" 
              icon="share-o" 
              class="action-button share-button"
              :disabled="!isActivityDataComplete(item) || isExpired(item)"
              @click.stop="handleShare(item)"
            >
              转发
            </van-button>
            <van-button 
              :type="getRegisterButtonType(item)" 
              size="small" 
              :icon="getRegisterButtonIcon(item)"
              class="action-button register-button"
              :disabled="!canRegister(item)"
              @click.stop="handleRegister(item)"
            >
              {{ getRegisterButtonText(item) }}
            </van-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, nextTick } from 'vue';
import { formatActivityTimeRange, formatDeadline } from '../utils/dateTime';

const scrollContainerRef = ref<HTMLElement | null>(null);
const getScrollContainer = () => scrollContainerRef.value;

interface Props {
  items: any[];
  loading: boolean;
  finished?: boolean;
  emptyText?: string;
  useInfiniteScroll?: boolean;
}

onMounted(() => {
  nextTick(() => {
    console.log('Activity滚动容器:', scrollContainerRef.value);
  });
});

const props = withDefaults(defineProps<Props>(), {
  emptyText: '暂无活动',
  useInfiniteScroll: true,
  finished: false,
});

const emit = defineEmits(['load-more', 'card-click', 'share', 'register']);

const internalLoading = ref(false);

watch(
  () => props.loading,
  (val) => {
    internalLoading.value = val;
  },
  { immediate: true }
);

const onLoadMore = () => {
  console.log('触发加载更多活动');
  emit('load-more');
};

// 获取活动状态
const getActivityStatus = (item: any) => {
  if (isExpired(item)) {
    return '报名已截止';
  }
  // 如果有明确的状态字段，优先使用
  if (item.status) {
    return item.status;
  }
  
  // 对于"正在报名中的活动"列表，默认状态应该是报名中
  // 我们可以通过其他字段来判断状态
  const now = new Date();
  
  // 检查活动时间
  const startDate = item.startdate ? new Date(item.startdate) : null;
  const endDate = item.enddate ? new Date(item.enddate) : null;
  
  if (startDate && endDate) {
    if (now < startDate) {
      return '报名中'; // 活动还没开始，处于报名阶段
    } else if (now >= startDate && now <= endDate) {
      return '进行中'; // 活动正在进行
    }
  }
  
  // 默认状态为报名中
  return '报名中';
};

// 获取状态标签类型
const getStatusTagType = (item: any) => {
  const status = getActivityStatus(item);
  switch (status) {
    case '进行中':
      return 'success';
    case '即将开始':
      return 'warning';
    case '已结束':
    case '报名已截止':
      return 'default';
    case '报名中':
      return 'primary';
    default:
      return 'primary';
  }
};

// 格式化活动时间 - 使用统一的时间格式化工具
const formatActivityTime = (item: any) => {
  if (item.startdate && item.enddate) {
    return formatActivityTimeRange(item.startdate, item.enddate);
  } else if (item.start_time && item.end_time) {
    return formatActivityTimeRange(item.start_time, item.end_time);
  } else if (item.startdate) {
    return formatActivityTimeRange(item.startdate, '');
  } else if (item.start_time) {
    return formatActivityTimeRange(item.start_time, '');
  }
  return '时间待定';
};

// 格式化参与人数
const formatParticipantCount = (item: any) => {
  if (item.current_participants !== undefined && item.max_participants !== undefined) {
    return `${item.current_participants}/${item.max_participants}人`;
  } else if (item.participant_count !== undefined) {
    return `${item.participant_count}人已报名`;
  } else if (item.nums !== undefined) {
    return `${item.nums}人已报名`;
  } else if (item.max_participants !== undefined) {
    return `限${item.max_participants}人`;
  }
  return '暂无数据';
};

// 格式化活动费用
const formatActivityFee = (item: any) => {
  if (item.isfree === '免费' || item.price === 0) {
    return '免费';
  } else if (item.price !== undefined) {
    return `¥${item.price}`;
  } else if (item.isfree) {
    return item.isfree;
  }
  return '免费'; // 默认静态数据
};

// 获取活动形式图标
const getActivityFormIcon = (item: any) => {
  const form = item.activity_type || item.activity_form || item.form_type || '';
  switch (form) {
    case '在线直播':
    case 'online_live':
      return 'play-circle-o';
    case '线下组织':
    case 'offline_organize':
      return 'shop-o';
    case '在线答题':
    case 'online_quiz':
      return 'edit';
    case '线上活动':
    case 'online_activity':
      return 'desktop-o';
    case '混合活动':
    case 'hybrid_activity':
      return 'exchange';
    default:
      return 'medal-o';
  }
};

// 获取活动形式样式类
const getActivityFormClass = (item: any) => {
  const form = item.activity_type || item.activity_form || item.form_type || '';
  switch (form) {
    case '在线直播':
    case 'online_live':
      return 'form-live';
    case '线下组织':
    case 'offline_organize':
      return 'form-offline';
    case '在线答题':
    case 'online_quiz':
      return 'form-quiz';
    case '线上活动':
    case 'online_activity':
      return 'form-online';
    case '混合活动':
    case 'hybrid_activity':
      return 'form-hybrid';
    default:
      return 'form-default';
  }
};

// 格式化活动形式
const formatActivityForm = (item: any) => {
  const form = item.activity_type || item.activity_form || item.form_type || '';
  
  // 如果是英文代码，转换为中文
  const formMap: { [key: string]: string } = {
    'online_live': '在线直播',
    'offline_organize': '线下组织',
    'online_quiz': '在线答题',
    'online_activity': '线上活动',
    'hybrid_activity': '混合活动'
  };
  
  return formMap[form] || form || '暂无数据';
};

// 判断活动是否已过期
const isExpired = (item: any) => {
  const deadlineStr = item.registration_deadline || item.dieline || item.deadline;
  if (!deadlineStr) {
    return false;
  }
  const regex = /^(\d{2})\/(\d{2}) (\d{2}):(\d{2})$/;
  const match = deadlineStr.match(regex);
  if (match) {
    const now = new Date();
    const year = now.getFullYear();
    const month = parseInt(match[1], 10) - 1;
    const day = parseInt(match[2], 10);
    const hours = parseInt(match[3], 10);
    const minutes = parseInt(match[4], 10);
    const deadlineDate = new Date(year, month, day, hours, minutes);
    return deadlineDate < now;
  } else {
    const deadlineDate = new Date(deadlineStr);
    if (isNaN(deadlineDate.getTime())) {
      return false;
    }
    return deadlineDate < new Date();
  }
};

// 判断活动数据是否完整
const isActivityDataComplete = (item: any) => {
  return item && item.name && item.name.trim() !== '';
};

// 处理转发按钮点击
const handleShare = (item: any) => {
  if (!isActivityDataComplete(item)) {
    return;
  }
  emit('share', item);
};

// 处理报名按钮点击
const handleRegister = (item: any) => {
  emit('register', item);
};

// 判断是否可以报名
const canRegister = (item: any) => {
  if (isExpired(item)) {
    return false;
  }
  if (item.current_participants && item.max_participants && 
      item.current_participants >= item.max_participants) {
    return false;
  }
  return true;
};

// 获取报名按钮类型
const getRegisterButtonType = (item: any) => {
  if (!canRegister(item)) {
    return 'default';
  }
  const status = getActivityStatus(item);
  if (status === '报名中') {
    return 'primary';
  } else if (status === '进行中') {
    return 'success';
  }
  return 'primary';
};

// 获取报名按钮图标
const getRegisterButtonIcon = (item: any) => {
  if (!canRegister(item)) {
    return 'cross';
  }
  const status = getActivityStatus(item);
  if (status === '报名中') {
    return 'add-o';
  } else if (status === '进行中') {
    return 'play-circle-o';
  }
  return 'add-o';
};

// 获取报名按钮文本
const getRegisterButtonText = (item: any) => {
  if (isExpired(item)) {
    return '报名已截止';
  }
  if (!canRegister(item)) {
    if (item.current_participants && item.max_participants && 
        item.current_participants >= item.max_participants) {
      return '报名满员';
    }
    return '无法报名';
  }
  const status = getActivityStatus(item);
  if (status === '报名中') {
    return '立即报名';
  } else if (status === '进行中') {
    return '正在进行';
  }
  return '立即报名';
};

// 格式化活动地点
const formatActivityLocation = (item: any) => {
  return item.location || item.address || '暂无数据';
};

// 格式化报名截止时间
const formatRegistrationDeadline = (item: any) => {
  const deadline = item.dieline || item.registration_deadline || item.deadline;
  if (!deadline) return '暂无数据';
  return formatDeadline(deadline);
};

// 格式化活动主办方
const formatOrganizer = (item: any) => {
  return item.leader || item.organizer || '暂无数据';
};

// 格式化联系方式
const formatContact = (item: any) => {
  return item.contact || '暂无数据';
};

// 获取高亮样式
const getHighlightStyle = (type: 'time' | 'category' | 'deadline' | 'contact' | 'location' | 'participants' | 'form' | 'fee' | 'organizer') => {
  const styles = {
    time: { backgroundColor: '#e8f4ff', color: '#4b8bf4' },
    category: { backgroundColor: '#e6f7ef', color: '#059669' },
    deadline: { backgroundColor: '#e8f4ff', color: '#4b8bf4' }, // 使用与 'time' 类似的蓝色系
    contact: { backgroundColor: '#fffbe6', color: '#fa8c16' },
    location: { backgroundColor: '#f0f5ff', color: '#2f54eb' },
    participants: { backgroundColor: '#f6ffed', color: '#52c41a' },
    form: { backgroundColor: '#e6fffb', color: '#13c2c2' },
    fee: { backgroundColor: '#fff0f6', color: '#eb2f96' },
    organizer: { backgroundColor: '#f9f0ff', color: '#722ed1' },
  };
  const baseStyle = {
    padding: '8px 12px',
    borderRadius: '8px',
    marginBottom: '12px',
  };

  // 如果类型存在于样式中，则返回合并后的样式
  if (type in styles) {
    return { ...baseStyle, ...styles[type] };
  }

  // 否则，返回一个默认的、不带背景色的样式对象
  return {
    padding: '0',
    borderRadius: '0',
    marginBottom: '16px', // 保持与其他项的间距
    backgroundColor: 'transparent',
    color: '#333' // 默认文本颜色
  };
};
</script>

<style scoped>
.activity-common-card-list {
  flex: 1;
  overflow-y: auto;
}

.activity-status-tag {
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 3;
  font-size: 12px !important;
  font-weight: 600 !important;
}

.activity-type-tag {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 3;
  font-size: 12px !important;
  font-weight: 600 !important;
}

.activity-card {
  border-radius: 16px;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  margin-bottom: 20px;
  cursor: pointer;
  border: 1px solid rgba(16, 172, 132, 0.1);
}

.activity-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 12px 32px rgba(16, 172, 132, 0.15);
  border-color: rgba(16, 172, 132, 0.3);
}

.activity-card.expired {
  cursor: not-allowed;
  opacity: 0.7;
  background: #f7f7f7;
}

.activity-card.expired:hover {
  transform: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border-color: rgba(16, 172, 132, 0.1);
}

.card-image-container {
  position: relative;
  overflow: hidden;
}

.activity-image {
  width: 100%;
  height: 180px;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.activity-card:hover .activity-image {
  transform: scale(1.05);
}

.image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.5), transparent);
}

.card-content {
  padding: 20px;
}

.activity-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 10px;
  color: #333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
}

.activity-description {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 16px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
}

.activity-details {
  margin-bottom: 20px;
  padding: 12px;
  background: rgba(16, 172, 132, 0.02);
  border-radius: 8px;
  border-left: 3px solid #10ac84;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  gap: 14px;
  margin-bottom: 16px;
  font-size: 15px;
}

.detail-item .van-icon {
  color: #10ac84;
  font-size: 18px;
  margin-top: 3px;
  flex-shrink: 0;
}

:deep(.detail-item.detail-item-time),
:deep(.detail-item.detail-item-category),
:deep(.detail-item.detail-item-deadline),
:deep(.detail-item.detail-item-contact) {
  padding: 8px 12px;
  border-radius: 8px;
  margin-bottom: 12px !important;
}

:deep(.detail-item-time) {
  background-color: #e8f4ff !important;
}
:deep(.detail-item-time .detail-value),
:deep(.detail-item-time .van-icon) {
  color: #4b8bf4 !important;
}

:deep(.detail-item-category) {
  background-color: #e6f7ef !important;
}
:deep(.detail-item-category .detail-value),
:deep(.detail-item-category .van-icon) {
  color: #059669 !important;
}

:deep(.detail-item-deadline) {
  background-color: #fff1f0 !important;
}
:deep(.detail-item-deadline .detail-value),
:deep(.detail-item-deadline .van-icon) {
  color: #ff4d4f !important;
}

:deep(.detail-item-contact) {
  background-color: #fffbe6 !important;
}
:deep(.detail-item-contact .detail-value),
:deep(.detail-item-contact .van-icon) {
  color: #fa8c16 !important;
}

.detail-content {
  display: flex; /* 添加 flex 布局 */
  align-items: center; /* 垂直居中对齐 */
  flex: 1;
  min-width: 0;
}

.detail-label {
  color: #999;
  font-size: 13px;
  margin-right: 8px; /* 标签和值之间的间距 */
  white-space: nowrap; /* 防止标签换行 */
  font-weight: 500;
}

.detail-value {
  color: #333;
  font-size: 15px;
  font-weight: 600;
  word-break: break-word;
  line-height: 1.4;
  flex-grow: 1; /* 允许值占据剩余空间 */
  text-align: left; /* 文本左对齐 */
}

.free-activity {
  color: #10ac84 !important;
  background: rgba(16, 172, 132, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 700;
}

.activity-form {
  padding: 3px 8px;
  border-radius: 6px;
  font-weight: 600;
  font-size: 14px;
}

.form-live {
  background: linear-gradient(135deg, #ff6b6b, #ffd93d);
  color: #fff;
}

.form-offline {
  background: linear-gradient(135deg, #4ecdc4, #44a08d);
  color: #fff;
}

.form-quiz {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #fff;
}

.form-online {
  background: linear-gradient(135deg, #f093fb, #f5576c);
  color: #fff;
}

.form-hybrid {
  background: linear-gradient(135deg, #ffecd2, #fcb69f);
  color: #333;
}

.form-default {
  background: rgba(16, 172, 132, 0.1);
  color: #10ac84;
}

.activity-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #f5f5f5;
}

.footer-left {
  flex: 1;
  min-width: 0;
}

.footer-right {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.activity-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.activity-tag {
  display: inline-block;
  padding: 4px 8px;
  background-color: rgba(16, 172, 132, 0.1);
  color: #10ac84;
  font-size: 11px;
  border-radius: 10px;
  font-weight: 500;
  white-space: nowrap;
}

.activity-heat {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #ff6b35;
  font-size: 12px;
  font-weight: 500;
}

.activity-heat .van-icon {
  font-size: 14px;
}

.activity-actions {
  display: flex;
  justify-content: space-between;
  gap: 12px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.action-button {
  flex: 1;
  height: 36px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s ease;
}

.share-button {
  background: rgba(16, 172, 132, 0.1);
  border: 1px solid rgba(16, 172, 132, 0.3);
  color: #10ac84;
}

.share-button:hover {
  background: rgba(16, 172, 132, 0.2);
  border-color: #10ac84;
  transform: translateY(-1px);
}

.register-button {
  font-weight: 700;
}

.register-button:not(:disabled):hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 172, 132, 0.3);
}

.register-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.loading-container {
  padding: 40px 0;
  text-align: center;
  color: #666;
}

/* 响应式调整 */
@media (max-width: 480px) {
  .activity-image {
    height: 160px;
  }
  
  .card-content {
    padding: 16px;
  }
  
  .activity-title {
    font-size: 16px;
  }
  
  .activity-description {
    font-size: 13px;
  }
  
  .detail-item {
    font-size: 14px;
    gap: 12px;
    margin-bottom: 14px;
  }
  
  .detail-item .van-icon {
    font-size: 16px;
  }
  
  .detail-label {
    font-size: 12px;
  }
  
  .detail-value {
    font-size: 14px;
  }
  
  .activity-tags {
    gap: 4px;
  }
  
  .activity-tag {
    font-size: 10px;
    padding: 3px 6px;
  }
  
  .activity-actions {
    gap: 8px;
    margin-top: 12px;
    padding-top: 12px;
  }
  
  .action-button {
    height: 32px;
    font-size: 13px;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate__fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}

/* 减少动画的用户偏好 */
@media (prefers-reduced-motion: reduce) {
  .activity-card,
  .activity-image {
    transition: none;
  }

  .animate__fadeInUp {
    animation: none;
  }
}
</style>
