import { ref, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import GlobalHeader from '../../components/GlobalHeader.vue';
import GlobalFooter from '../../components/GlobalFooter.vue';
import CommonCardList from '../../components/CommonCardList.vue';
import { getDeptCategoriesWithRetry, getDeptCategories } from './api'; // 假设支持分页参数
const router = useRouter();
const loading = ref(false);
const finished = ref(false);
const items = ref([]);
const page = ref(1);
const activeTab = ref(0);
const pageSize = ref(5); // 每次加载10条数据
const commonCardListRef = ref(null);
// 标签数据
const tabs = ref([]);
// 加载标签数据
const loadCategories = async () => {
    const res = await getDeptCategories();
    console.log('获取到领导分类sdfsdfsd:', res);
    // 将API返回的分类数据转换为标签格式
    const categoryTabs = res.categories.map((category) => ({
        id: category.value,
        name: category.label
    }));
    // 更新标签数据，保留"全部"标签
    tabs.value = [...categoryTabs];
};
// 监听标签变化
watch(activeTab, (newVal) => {
    // 重置分页相关
    page.value = 1;
    items.value = [];
    finished.value = false;
    // 重新加载数据
    loadItems();
    // 滚动到顶部
    commonCardListRef.value?.resetScroll();
});
const loadItems = async () => {
    if (finished.value || loading.value)
        return;
    loading.value = true;
    const currentTabId = tabs.value[activeTab.value]?.id;
    console.log('当前选中标签ID:', currentTabId);
    try {
        const res = await getDeptCategoriesWithRetry(currentTabId, page.value, pageSize.value);
        items.value.push(...res.results);
        page.value += 1;
        finished.value = res.is_last_page === true;
    }
    catch (error) {
        console.error('加载失败:', error);
    }
    finally {
        loading.value = false;
    }
};
const handleCardClick = (news) => {
    router.push({ name: 'DeptDetail', params: { id: news.id } });
};
onMounted(async () => {
    await loadCategories();
    loadItems();
});
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "news-page category-tabs-container" },
});
/** @type {[typeof GlobalHeader, ]} */ ;
// @ts-ignore
const __VLS_0 = __VLS_asFunctionalComponent(GlobalHeader, new GlobalHeader({
    title: "特色科室",
}));
const __VLS_1 = __VLS_0({
    title: "特色科室",
}, ...__VLS_functionalComponentArgsRest(__VLS_0));
const __VLS_3 = {}.VanTabs;
/** @type {[typeof __VLS_components.VanTabs, typeof __VLS_components.vanTabs, typeof __VLS_components.VanTabs, typeof __VLS_components.vanTabs, ]} */ ;
// @ts-ignore
const __VLS_4 = __VLS_asFunctionalComponent(__VLS_3, new __VLS_3({
    active: (__VLS_ctx.activeTab),
    ...{ class: "category-tabs" },
    sticky: true,
    stickyOffsetTop: (0),
    swipeable: true,
}));
const __VLS_5 = __VLS_4({
    active: (__VLS_ctx.activeTab),
    ...{ class: "category-tabs" },
    sticky: true,
    stickyOffsetTop: (0),
    swipeable: true,
}, ...__VLS_functionalComponentArgsRest(__VLS_4));
__VLS_6.slots.default;
for (const [tab] of __VLS_getVForSourceType((__VLS_ctx.tabs))) {
    const __VLS_7 = {}.VanTab;
    /** @type {[typeof __VLS_components.VanTab, typeof __VLS_components.vanTab, typeof __VLS_components.VanTab, typeof __VLS_components.vanTab, ]} */ ;
    // @ts-ignore
    const __VLS_8 = __VLS_asFunctionalComponent(__VLS_7, new __VLS_7({
        key: (tab.id),
        title: (tab.name),
    }));
    const __VLS_9 = __VLS_8({
        key: (tab.id),
        title: (tab.name),
    }, ...__VLS_functionalComponentArgsRest(__VLS_8));
    __VLS_10.slots.default;
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "section-container" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "news-grid" },
    });
    /** @type {[typeof CommonCardList, ]} */ ;
    // @ts-ignore
    const __VLS_11 = __VLS_asFunctionalComponent(CommonCardList, new CommonCardList({
        ...{ 'onLoadMore': {} },
        ...{ 'onCardClick': {} },
        ref: "commonCardListRef",
        items: (__VLS_ctx.items),
        loading: (__VLS_ctx.loading),
        finished: (__VLS_ctx.finished),
        useInfiniteScroll: (true),
    }));
    const __VLS_12 = __VLS_11({
        ...{ 'onLoadMore': {} },
        ...{ 'onCardClick': {} },
        ref: "commonCardListRef",
        items: (__VLS_ctx.items),
        loading: (__VLS_ctx.loading),
        finished: (__VLS_ctx.finished),
        useInfiniteScroll: (true),
    }, ...__VLS_functionalComponentArgsRest(__VLS_11));
    let __VLS_14;
    let __VLS_15;
    let __VLS_16;
    const __VLS_17 = {
        onLoadMore: (__VLS_ctx.loadItems)
    };
    const __VLS_18 = {
        onCardClick: (__VLS_ctx.handleCardClick)
    };
    /** @type {typeof __VLS_ctx.commonCardListRef} */ ;
    var __VLS_19 = {};
    var __VLS_13;
    var __VLS_10;
}
var __VLS_6;
/** @type {[typeof GlobalFooter, ]} */ ;
// @ts-ignore
const __VLS_21 = __VLS_asFunctionalComponent(GlobalFooter, new GlobalFooter({}));
const __VLS_22 = __VLS_21({}, ...__VLS_functionalComponentArgsRest(__VLS_21));
/** @type {__VLS_StyleScopedClasses['news-page']} */ ;
/** @type {__VLS_StyleScopedClasses['category-tabs-container']} */ ;
/** @type {__VLS_StyleScopedClasses['category-tabs']} */ ;
/** @type {__VLS_StyleScopedClasses['section-container']} */ ;
/** @type {__VLS_StyleScopedClasses['news-grid']} */ ;
// @ts-ignore
var __VLS_20 = __VLS_19;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            GlobalHeader: GlobalHeader,
            GlobalFooter: GlobalFooter,
            CommonCardList: CommonCardList,
            loading: loading,
            finished: finished,
            items: items,
            activeTab: activeTab,
            commonCardListRef: commonCardListRef,
            tabs: tabs,
            loadItems: loadItems,
            handleCardClick: handleCardClick,
        };
    },
});
export default (await import('vue')).defineComponent({
    setup() {
        return {};
    },
});
; /* PartiallyEnd: #4569/main.vue */
