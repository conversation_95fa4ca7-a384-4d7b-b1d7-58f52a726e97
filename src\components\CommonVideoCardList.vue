<template>
  <div class="common-card-list" ref="scrollContainerRef">
    <!-- 首次加载中 -->
    <div v-if="loading && !items.length" class="loading-container">
      <van-loading size="24px">加载中...</van-loading>
    </div>

    <!-- 空状态 -->
    <van-empty v-else-if="!items.length" :description="emptyText" />

    <!-- 列表 -->
    <van-list v-else-if="useInfiniteScroll" v-model:loading="internalLoading" :finished="finished" finished-text="没有更多了"
      @load="onLoadMore" :immediate-check="false" :scroll-container="getScrollContainer">
      <div v-for="item in items" :key="item.id" class="news-card animate__animated animate__fadeInUp"
        @click="$emit('card-click', item)">
        <div class="video-thumbnail-wrapper">
          <img :src="item.thumbnail" :alt="item.name" class="video-thumbnail" />
          <div class="play-button">
            <van-icon name="play" size="24" color="#fff" />
          </div>
          <div class="video-duration">{{ item.duration }}</div>
        </div>
        <div class="video-content">
          <h3 class="video-title">{{ item.name }}</h3>
          <p class="video-summary">{{ item.desc }}</p>
          <div class="video-meta">
            <span class="author">{{ item.creater }}</span>
            <span class="date">{{ item.create_time }}</span>
          </div>
        </div>
        <div class="card-meta">
          <div class="meta-left" v-if="item.cat_display">
            <span class="category">{{ item.cat_display }}</span>
          </div>
          <div class="meta-right" v-if="item.tags && item.tags.length">
            <div class="card-tags">
              <span v-for="(tag, index) in item.tags" :key="index" class="tag">{{ tag }}</span>
            </div>
          </div>
        </div>
      </div>
    </van-list>

    <!-- 非无限滚动模式 -->
    <div v-else>
      <div v-for="item in items" :key="item.id" class="news-card animate__animated animate__fadeInUp"
        @click="$emit('card-click', item)">
        <div class="card-image-container">
          <van-tag v-if="item.tag" type="danger" size="medium" class="news-tag" round>{{ item.tag }}</van-tag>
          <img :src="item.thumbnail" :alt="item.name" class="news-image" />
          <div class="image-overlay"></div>
        </div>
        <div class="card-content">
          <h3 class="news-title">{{ item.name }}</h3>
          <p class="news-description">{{ item.desc }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, nextTick } from 'vue';
const scrollContainerRef = ref<HTMLElement | null>(null);
const getScrollContainer = () => scrollContainerRef.value;

interface Props {
  items: any[];
  loading: boolean;
  finished?: boolean;
  emptyText?: string;
  useInfiniteScroll?: boolean;
}

onMounted(() => {
  nextTick(() => {
    console.log('滚动容器:', scrollContainerRef.value);
  });
});

const props = withDefaults(defineProps<Props>(), {
  emptyText: '暂无内容',
  useInfiniteScroll: true,
  finished: false,
});

const emit = defineEmits(['load-more', 'card-click']);
const internalLoading = ref(false);

watch(() => props.loading, (val) => {
  internalLoading.value = val;
}, { immediate: true });

const onLoadMore = () => {
  console.log('触发加载更多');
  emit('load-more');
};

const resetScroll = () => {
  if (scrollContainerRef.value) {
    scrollContainerRef.value.scrollTop = 0;
  }
};

defineExpose({
  resetScroll,
});
</script>

<style scoped>
.common-card-list {
  flex: 1;
  overflow-y: auto;
}

.news-card {
  border-radius: 12px;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s;
  margin-bottom: 12px;
  cursor: pointer;
}

.news-card:hover {
  transform: translateY(-2px);
}

.video-thumbnail-wrapper {
  position: relative;
  height: 200px;
  overflow: hidden;
  width: 100%;
  background: #000;
}

.video-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.video-duration,
.video-quality,
.video-tag,
.featured-badge {
  position: absolute;
  font-size: 11px;
  border-radius: 4px;
  font-weight: 500;
  padding: 2px 6px;
  color: #fff;
}

.video-duration {
  bottom: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.8);
}

.video-quality {
  top: 8px;
  right: 8px;
  background: rgba(255, 140, 105, 0.9);
}

.video-tag {
  top: 8px;
  left: 8px;
  backdrop-filter: blur(10px);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  white-space: nowrap;
}

.featured-badge {
  top: 40px;
  left: 8px;
  background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
  color: #333;
  text-shadow: none;
}

.video-content {
  padding: 16px;
  box-sizing: border-box;
}

.video-title,
.news-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
}

.video-summary,
.news-description {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
}

.video-meta,
.card-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #999;
  flex-wrap: wrap;
  gap: 8px;
}

.author {
  color: #ff8c69;
  font-weight: 500;
}

.card-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tag {
  display: inline-block;
  padding: 2px 8px;
  background-color: #f0f5ff;
  color: #1989fa;
  font-size: 10px;
  border-radius: 4px;
  margin-bottom: 4px;
}

.card-image-container {
  position: relative;
}

.news-image {
  width: 100%;
  height: 120px;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 30px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.3), transparent);
}

.card-content {
  padding: 10px;
}

.loading-container {
  padding: 30px 0;
  text-align: center;
}
</style>
