<template>
  <div class="activity-page">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    
    <!-- 使用通用顶部导航栏 -->
    <GlobalHeader title="精彩活动" @left-click="handleBack" />
    <Carousel position="3" />
    
    <!-- 功能导航区域 -->
    <div class="section-container">
      <div class="section-header">
        <div class="section-title">
          <van-icon name="apps-o" color="#4b8bf4" size="18" />
          <span>活动导航</span>
        </div>
      </div>

      <!-- 功能导航：4个按钮一行排列 -->
      <van-grid :column-num="4" :border="false" :gutter="10" class="function-grid navigation-row">
        <van-grid-item v-for="(item, index) in navigationItems" :key="index" class="animate__animated animate__fadeInUp"
          :style="{ animationDelay: index * 0.05 + 's' }">
          <div class="grid-item-content" :class="{ active: isActiveRoute(item) }" @click="onFunctionClick(item)">
            <div class="icon-wrapper" :class="{ active: isActiveRoute(item) }" :style="getIconStyle(item)">
              <van-icon :name="item.icon" :color="getIconColor(item)" size="24" />
            </div>
            <span class="grid-text" :class="{ active: isActiveRoute(item) }">{{ item.text }}</span>
          </div>
        </van-grid-item>
      </van-grid>
    </div>

    <!-- 正在报名中的活动列表 -->
    <div class="section-container">
      <div class="section-header">
        <div class="section-title">
          <van-icon name="play-circle-o" color="#10ac84" size="18" />
          <span>正在报名中的活动</span>
        </div>
      </div>
      
      <ActivityCommonCardList :items="homeData.activity_data" :loading="loading" :finished="true"
        :use-infinite-scroll="false" @card-click="handleCardClick" @share="handleShare" @register="handleRegister" />
    </div>

    <!-- 活动回顾版块 -->
    <div class="section-container">
      <div class="section-header">
        <div class="section-title">
          <van-icon name="photo-o" color="#ff6b35" size="18" />
          <span>活动回顾</span>
        </div>
        <div class="section-more" @click="handleViewMoreReviews">
          <span>查看更多</span>
          <van-icon name="arrow" size="12" />
        </div>
      </div>
      
      <!-- 活动回顾列表 -->
      <div class="activity-reviews">
        <div v-if="reviewLoading" class="loading-container">
          <van-loading size="24px">加载中...</van-loading>
        </div>
        
        <van-empty v-else-if="!activityReviewGridItems.length" description="暂无活动回顾" />
        
        <!-- 活动回顾网格 -->
        <GridCard 
          v-else
          :items="activityReviewGridItems" 
          @card-click="handleReviewGridClick"
        />
      </div>
    </div>

    <!-- 返回顶部 -->
    <van-back-top right="16" bottom="80" />

    <!-- 使用通用底部装饰 -->
    <GlobalFooter />


  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onActivated, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { showToast } from 'vant';
import 'vant/es/toast/style'; // ✅ 必须引入 Toast 样式，解决白色弹窗问题
import GlobalHeader from '../../components/GlobalHeader.vue'; // 导入通用页头组件
import GlobalFooter from '../../components/GlobalFooter.vue'; // 导入通用页脚组件
import Carousel from "../Home/components/Carousel.vue"; // 导入轮播图组件
import ActivityCommonCardList from '../../components/ActivityCommonCardList.vue';
import { getHomeActivityList } from './api';
import { getActivityNewsListlWithRetry } from '../ActivityNews/api';
import GridCard from '../../components/GridCard.vue';
import { shareActivity, shareWithSystemMenu, canUseNativeShare, isWeChatEnvironment } from '../../utils/share'; // 导入分享工具


const router = useRouter();
const route = useRoute();
const loading = ref(false);
const reviewLoading = ref(false);

// 功能导航数据（4个按钮一行排列）
const navigationItems = ref([
  {
    icon: "calendar-o",
    text: "本月活动",
    bgColor: "#e8f4ff",
    iconColor: "#4b8bf4",
    route: "/activity-list?type=current",
    routeName: "ActivityList",
    queryType: "current"
  },
  {
    icon: "clock-o",
    text: "历史活动",
    bgColor: "#f0e6ff",
    iconColor: "#7c3aed",
    route: "/activity-list?type=past",
    routeName: "ActivityList",
    queryType: "past"
  },
  {
    icon: "todo-list-o",
    text: "活动预告",
    bgColor: "#e6f7ef",
    iconColor: "#059669",
    route: "/activity-list?type=upcoming",
    routeName: "ActivityList",
    queryType: "upcoming"
  },
  {
    icon: "newspaper-o",
    text: "活动新闻",
    bgColor: "#ffebe6",
    iconColor: "#ff8c69",
    route: "/activity-news-list",
    routeName: "ActivityNewsList"
  },
]);

const homeData = ref<{
  activity_data: any[];
}>({
  activity_data: [],
});

const activityReviews = ref<any[]>([]);
const activityGridItems = ref<any[]>([]);
const activityReviewGridItems = ref<any[]>([]);

// 加载活动数据
const loadActivities = async () => {
  try {
    loading.value = true;

    const res = await getHomeActivityList();
    console.log('获取首页活动列表成功:', res);
    
    homeData.value.activity_data = res.items.activity_data;

  } catch (error) {
    console.error('获取活动列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 加载活动回顾数据
const loadActivityReviews = async () => {
  try {
    reviewLoading.value = true;
    
    // 调用活动新闻API获取活动回顾数据
    const res = await getActivityNewsListlWithRetry({ page_size: 6 });
    
    // 将活动新闻数据转换为GridCard需要的格式
    activityReviewGridItems.value = res.results.map((item: any) => ({
      id: item.id,
      title: item.name,
      image: item.thumbnail,
      badge: '回顾',
      originalData: item
    }));
    
  } catch (error) {
    console.error('获取活动回顾失败:', error);
    // 如果活动新闻API失败，回退到使用活动数据
    try {
      const fallbackRes = await getHomeActivityList();
      activityReviewGridItems.value = fallbackRes.items.activity_data.slice(0, 6).map((item: any) => ({
        id: item.id,
        title: item.name,
        image: item.thumbnail,
        badge: '回顾',
        originalData: item
      }));
    } catch (fallbackError) {
      console.error('获取活动回顾数据完全失败:', fallbackError);
    }
  } finally {
    reviewLoading.value = false;
  }
};

// 初始化加载
onMounted(() => {
  loadActivities();
  loadActivityReviews();
});

// 页面激活时重新验证数据
onActivated(() => {
  console.log('活动页面被激活，检查数据状态');
  // 如果数据为空，重新加载
  if (!homeData.value.activity_data || homeData.value.activity_data.length === 0) {
    console.log('检测到活动数据为空，重新加载');
    loadActivities();
  }
  if (!activityReviewGridItems.value || activityReviewGridItems.value.length === 0) {
    console.log('检测到活动回顾数据为空，重新加载');
    loadActivityReviews();
  }
});

// 监听路由变化，确保从其他页面返回时数据正确
watch(() => route.name, (newName, oldName) => {
  if (newName === 'Activity' && oldName && oldName !== 'Activity') {
    console.log(`从 ${String(oldName)} 页面返回到活动页面，验证数据状态`);
    // 延迟一小段时间再检查，确保页面完全加载
    setTimeout(() => {
      if (!homeData.value.activity_data || homeData.value.activity_data.length === 0) {
        console.log('返回活动页面时检测到数据丢失，重新加载');
        loadActivities();
      }
    }, 100);
  }
});

// 处理活动点击
const handleCardClick = (news: any) => {
  router.push({ 
    name: 'ActivityDetail', 
    params: { id: news.id },
    query: { from: 'ongoing' } // 标记来源为正在进行中
  });
};

// 判断是否是当前激活的路由
const isActiveRoute = (item: any) => {
  if (item.queryType) {
    // 对于有查询参数的路由，需要同时匹配路由名和查询参数
    return route.name === item.routeName && route.query.type === item.queryType;
  }
  // 对于没有查询参数的路由，只匹配路由名
  return route.name === item.routeName;
};

// 获取图标颜色
const getIconColor = (item: any) => {
  return isActiveRoute(item) ? '#fff' : item.iconColor;
};

// 获取图标样式
const getIconStyle = (item: any) => {
  if (isActiveRoute(item)) {
    return {
      background: `linear-gradient(135deg, ${item.iconColor}, ${item.iconColor}dd)`
    };
  }
  return {
    backgroundColor: item.bgColor
  };
};

// 处理功能导航点击
const onFunctionClick = (item: any) => {
  if (item.route) {
    router.push(item.route);
  }
};

// 页头按钮事件处理
const handleBack = () => {
  router.back();
};

// 查看更多活动回顾
const handleViewMoreReviews = () => {
  router.push('/activity-news-list');
};

const handleActivityGridClick = (item: any) => {
  // 从GridCard的originalData中获取原始活动数据
  const activityData = item.originalData || item;
  router.push({ name: 'ActivityDetail', params: { id: activityData.id } });
};

const handleReviewGridClick = (item: any) => {
  // 从GridCard的originalData中获取原始活动数据
  const activityData = item.originalData || item;
  // 活动回顾应该跳转到活动新闻详情页
  router.push({ name: 'ActivityNewsDetail', params: { id: activityData.id } });
};

// 处理分享按钮点击
const handleShare = async (item: any) => {
  console.log('=== 开始分享流程 ===');
  console.log('分享活动原始数据:', item);
  console.log('当前页面活动数据状态:', homeData.value.activity_data);
  
  // 第一层验证：检查传入的item是否有效
  if (!item) {
    console.error('分享失败：item为空', item);
    showToast('分享数据异常，请刷新页面后重试');
    return;
  }
  
  // 第二层验证：检查活动名称
  if (!item.name || typeof item.name !== 'string' || item.name.trim() === '') {
    console.error('分享失败：活动名称无效', { 
      name: item.name, 
      type: typeof item.name,
      item: item 
    });
    
    // 尝试从当前页面数据中查找对应的活动
    if (item.id && homeData.value.activity_data.length > 0) {
      const foundActivity = homeData.value.activity_data.find(activity => 
        activity.id === item.id || activity.id.toString() === item.id.toString()
      );
      
      if (foundActivity && foundActivity.name && foundActivity.name.trim() !== '') {
        console.log('从页面数据中找到完整的活动信息，使用完整数据进行分享');
        item = foundActivity;
      } else {
        console.error('无法找到完整的活动数据');
        showToast('活动数据不完整，请刷新页面后重试');
        return;
      }
    } else {
      showToast('活动数据加载中，请稍后再试');
      return;
    }
  }
  
  console.log('验证通过，开始执行分享:', item.name);
  
  try {
    // 首先尝试使用系统原生分享菜单（可以直接调起微信等应用）
    if (canUseNativeShare()) {
      try {
        console.log('尝试使用原生分享API');
        const success = await shareWithSystemMenu(item);
        if (success) {
          console.log('原生分享成功');
          showToast('分享成功');
          return;
        }
      } catch (error) {
        console.log('原生分享失败，尝试其他方式:', error);
      }
    }
    
    // 如果原生分享不可用或失败，使用备用方案
    console.log('使用备用分享方案');
    const result = await shareActivity(item);
    console.log('分享结果:', result);
    
    if (result.success) {
      switch (result.method) {
        case 'native':
          showToast('分享成功');
          break;
        case 'clipboard':
          if (isWeChatEnvironment()) {
            showToast('内容已复制，请在微信中长按粘贴分享');
          } else {
            showToast('内容已复制，请粘贴到微信等应用中分享');
          }
          break;
        default:
          showToast('分享成功');
      }
    } else {
      if (result.method === 'cancelled') {
        // 用户取消分享，不显示错误提示
        console.log('用户取消分享');
        return;
      }
      console.error('分享失败:', result);
      showToast('分享失败，请稍后重试');
    }
  } catch (error) {
    console.error('分享过程中发生异常:', error);
    showToast('分享失败，请稍后重试');
  }
  
  console.log('=== 分享流程结束 ===');
};

// 处理报名按钮点击
const handleRegister = (item: any) => {
  showToast(`报名活动: ${item.name}`);
  // 这里可以添加实际的报名逻辑
  // 比如跳转到报名页面、调用报名API等
  router.push({ name: 'ActivityRegistration', params: { id: item.id } });
};
</script>

<style>
@import "../../style/common.css";
</style>

<style scoped>
/* 基础容器样式 */
.activity-page {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  padding-bottom: 80px; /* 为页脚留出空间，参考知识页实现 */
  position: relative;
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
  box-sizing: border-box;
}

/* 功能导航网格样式 */
.function-grid {
  --van-grid-item-content-padding: 8px;
}

/* 功能导航区样式 */
:deep(.navigation-row .van-grid-item) {
  flex: 0 0 25%;
  max-width: 25%;
  box-sizing: border-box;
}

.grid-item-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  width: 100%;
  padding: 8px 2px;
  box-sizing: border-box;
  position: relative; /* 为底部指示器提供定位基准 */
  cursor: pointer;
  min-height: 70px;
}

.grid-item-content:hover {
  transform: translateY(-5px);
}

.icon-wrapper {
  width: 48px;
  height: 48px;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.grid-item-content:active .icon-wrapper {
  transform: scale(0.95);
}

/* 选中状态的图标容器 */
.icon-wrapper.active {
  border-radius: 24px !important; /* 圆形 */
  box-shadow: 0 2px 8px rgba(75, 139, 244, 0.3);
  transform: scale(1.05);
}

/* 选中状态的图标 */
.icon-wrapper.active .van-icon {
  color: #fff !important;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

.grid-text {
  font-size: 13px;
  font-weight: 500;
  color: #333;
  text-align: center;
  line-height: 1.2;
  transition: all 0.3s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

/* 选中状态的文字 */
.grid-text.active {
  color: #4b8bf4 !important;
  font-weight: 600;
}

/* 选中状态的底部指示器 */
.grid-item-content.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background: #4b8bf4;
  border-radius: 50%;
  animation: dotFadeIn 0.3s ease;
}

@keyframes dotFadeIn {
  0% {
    opacity: 0;
    transform: translateX(-50%) scale(0);
  }
  100% {
    opacity: 1;
    transform: translateX(-50%) scale(1);
  }
}

/* 响应式样式优化 */
@media (max-width: 414px) {
  .grid-text {
    font-size: 12px;
  }
  
  .grid-item-content {
    padding: 6px 1px;
    min-height: 65px;
  }
  
  .icon-wrapper {
    width: 42px;
    height: 42px;
    margin-bottom: 6px;
  }
}

@media (max-width: 374px) {
  .grid-text {
    font-size: 11px;
  }
  
  .grid-item-content {
    padding: 6px 1px;
    min-height: 60px;
  }
  
  .icon-wrapper {
    width: 38px;
    height: 38px;
    margin-bottom: 5px;
  }
}

/* 对于特别小的屏幕，进一步缩小字体 */
@media (max-width: 320px) {
  .grid-text {
    font-size: 10px;
    line-height: 1.1;
  }
  
  .grid-item-content {
    padding: 4px 1px;
    min-height: 55px;
  }
  
  .icon-wrapper {
    width: 36px;
    height: 36px;
    margin-bottom: 4px;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate__fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}

/* 减少动画的用户偏好 */
@media (prefers-reduced-motion: reduce) {
  .grid-item-content,
  .icon-wrapper {
    transition: none;
  }

  .animate__fadeInUp {
    animation: none;
  }
}

/* 确保所有元素不会超出容器 */
* {
  box-sizing: border-box;
}

img {
  max-width: 100%;
  height: auto;
}

/* 活动回顾版块样式 */
.activity-reviews {
  margin-top: 16px;
}

.reviews-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.section-more {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
  color: #999;
  cursor: pointer;
  transition: color 0.3s ease;
}

.section-more:hover {
  color: #4b8bf4;
}

.section-more .van-icon {
  transition: transform 0.3s ease;
}

.section-more:hover .van-icon {
  transform: translateX(2px);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #666;
}
</style>
