<template>
  <div class="knowledge-container">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />

    <!-- 使用通用顶部导航栏 -->
    <GlobalHeader title="中医知识" />
    <Carousel position="2" />

    <!-- 功能菜单区域 - 使用统一的section-container样式 -->
    <div class="section-container">
      <div class="section-header">
        <div class="section-title">
          <van-icon name="apps-o" color="#4b8bf4" size="18" />
          <span>功能导航</span>
        </div>
      </div>

      <!-- 第一排：2个功能项 -->
      <van-grid :column-num="2" :border="false" :gutter="10" class="function-grid first-row">
        <van-grid-item v-for="(item, index) in firstRowItems" :key="index" class="animate__animated animate__fadeInUp"
          :style="{ animationDelay: index * 0.05 + 's' }">
          <div class="grid-item-content" :class="{ active: isActiveRoute(item) }" @click="onFunctionClick(item)">
            <div class="icon-wrapper" :class="{ active: isActiveRoute(item) }" :style="getIconStyle(item)">
              <van-icon :name="item.icon" :color="getIconColor(item)" size="24" />
            </div>
            <span class="grid-text" :class="{ active: isActiveRoute(item) }">{{ item.text }}</span>
          </div>
        </van-grid-item>
      </van-grid>

      <!-- 第二排：3个功能项 -->
      <van-grid :column-num="3" :border="false" :gutter="10" class="function-grid second-row">
        <van-grid-item v-for="(item, index) in secondRowItems" :key="index" class="animate__animated animate__fadeInUp"
          :style="{ animationDelay: (index + 2) * 0.05 + 's' }">
          <div class="grid-item-content" :class="{ active: isActiveRoute(item) }" @click="onFunctionClick(item)">
            <div class="icon-wrapper" :class="{ active: isActiveRoute(item) }" :style="getIconStyle(item)">
              <van-icon :name="item.icon" :color="getIconColor(item)" size="24" />
            </div>
            <span class="grid-text" :class="{ active: isActiveRoute(item) }">{{ item.text }}</span>
          </div>
        </van-grid-item>
      </van-grid>
    </div>

    <!-- 精品文章专区 -->
    <ArticleGridSection 
      title="精品文章" 
      icon="description-o" 
      :showMore="false"
      :cultureData="homeData.culture_best_data" 
      :knowledgeData="homeData.knowledge_best_data"
      :caseData="homeData.case_best_data" 
    />
    
    <!-- 健康生活专区 -->
    <Healthy :cultureHealthyData="homeData.culture_healthy_data" :knowledgeHealthyData="homeData.knowledge_healthy_data"
      :caseHealthyData="homeData.case_healthy_data" />

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <van-loading type="spinner" color="#1989fa" />
      <p>加载中...</p>
    </div>

    <!-- 返回顶部 -->
    <van-back-top right="16" bottom="80" />

    <!-- 使用通用底部装饰 -->
    <GlobalFooter />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from "vue";
import { showToast } from "vant";
import { useRouter, useRoute } from "vue-router"; // 导入 useRouter 和 useRoute
import GlobalHeader from "../../components/GlobalHeader.vue"; // 导入通用页头组件
import GlobalFooter from "../../components/GlobalFooter.vue"; // 导入通用页脚组件
import Carousel from "../Home/components/Carousel.vue"; // 导入轮播组件
import ArticleGridSection from "../../components/ArticleGridSection.vue";
import Healthy from "./components/Healthy.vue";
import {
  getHomeDataList
} from "./api";

const router = useRouter(); // 初始化 router
const route = useRoute(); // 初始化 route

// 响应式数据
const loading = ref(false);
const error = ref("");
const refreshing = ref(false);

// 功能菜单数据 - 参考首页实现
const firstRowItems = ref([
  {
    icon: "photo-o",
    text: "科教图文",
    bgColor: "#fff0f0",
    iconColor: "#ff6b35",
    route: "/education-media",
    routeName: "EducationMedia",
  },
  {
    icon: "question-o",
    text: "中医知识",
    bgColor: "#e8f4ff",
    iconColor: "#4b8bf4",
    route: "/tcm-knowledge",
    routeName: "TcmKnowledge",
  },
]);

const secondRowItems = ref([
  {
    icon: "star-o",
    text: "中医文化",
    bgColor: "#f0e6ff",
    iconColor: "#7c3aed",
    route: "/culture",
    routeName: "Culture",
  },
  {
    icon: "records",
    text: "中医案例",
    bgColor: "#e6f7ef",
    iconColor: "#059669",
    route: "/cases",
    routeName: "Cases",
  },
  {
    icon: "video-o",
    text: "视频宣传",
    bgColor: "#ffebe6",
    iconColor: "#ff8c69",
    route: "/video",
    routeName: "Video",
  },
]);

const homeData = ref<{
  culture_best_data: any[];
  knowledge_best_data: any[];
  case_best_data: any[];
  culture_healthy_data: any[];
  knowledge_healthy_data: any[];
  case_healthy_data: any[];
}>({
  culture_best_data: [],
  knowledge_best_data: [],
  case_best_data: [],
  culture_healthy_data: [],
  knowledge_healthy_data: [],
  case_healthy_data: [],
});
// 获取文章列表
const fetchArticles = async () => {
  if (loading.value) return;

  try {
    loading.value = true;
    error.value = "";

    const res = await getHomeDataList({ source: 'knowledge' });
    homeData.value = res.items;
    console.log("获取到的文章列表数据:", homeData.value);
  } catch (err) {
    console.error("获取文章列表失败:", err);
    error.value = "获取文章列表失败，请稍后再试";
  } finally {
    loading.value = false;
    refreshing.value = false;
  }
};

// 判断是否是当前激活的路由
const isActiveRoute = (item: any) => {
  return route.name === item.routeName;
};

// 获取图标颜色
const getIconColor = (item: any) => {
  return isActiveRoute(item) ? '#fff' : item.iconColor;
};

// 获取图标样式
const getIconStyle = (item: any) => {
  if (isActiveRoute(item)) {
    return {
      background: `linear-gradient(135deg, ${item.iconColor}, ${item.iconColor}dd)`
    };
  }
  return {
    backgroundColor: item.bgColor
  };
};

const onFunctionClick = (item: any) => {
  console.log(`点击了功能项: ${item.text}, 路由路径: ${item.route}`);

  // 根据功能项跳转到对应的分类页面
  if (item.route) {
    // 使用 router.push 进行路由跳转
    router.push(item.route).catch((err) => {
      console.error("路由跳转失败:", err);
      showToast(`跳转到${item.text}失败，请稍后再试`);
    });
  } else {
    showToast(`${item.text}功能开发中`);
  }
};

onMounted(() => {
  fetchArticles();

});

</script>

<style>
@import "../../style/common.css";
</style>

<style src="./style.css"></style>
