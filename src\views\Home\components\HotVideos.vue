<template>
  <div class="section-container">
    <SectionHeader title="热门视频" icon="tv-o" :showMore="false" />

    <CommonVideoCardList :items="items" :loading="loading" :finished="finished" :use-infinite-scroll="true"
      @load-more="loadItems" @card-click="handleItemClick" />
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { showToast } from 'vant';
import { getVideoList } from '../api';
import SectionHeader from '../../../components/SectionHeader.vue'; // 引入通用标题组件
import CommonVideoCardList from '../../../components/CommonVideoCardList.vue';

const router = useRouter();
const loading = ref(false);
const error = ref("");
const refreshing = ref(false);
const pageSize = ref(4); // 每次加载4条数据
const finished = ref(false); // 是否已加载完所有数据

/// 列表数据
const items = ref<any[]>([]);

// 加载列表数据
const loadItems = async () => {
  if (loading.value) return;
  loading.value = true;

  try {
    const res = await getVideoList();
    console.log('获取到视频列表数据:', res);

    // 检查响应数据结构 - 修改这里以适应API返回的实际格式
    if (!res || !res.results || !Array.isArray(res.results)) {
      console.error('API响应格式不正确:', res);
      items.value = [];
      loading.value = false;
      return;
    }

    console.log('API返回的原始数据:', res.results);

    // 直接使用后台返回的结果顺序，不进行前端排序
    const limitedResults = res.results.slice(0, 4);
    console.log('限制数量后的数据:', limitedResults);

    // 将API返回的数据转换为组件需要的格式
    const formattedItems = limitedResults.map((item) => ({
      id: item.id,
      name: item.name,
      desc: item.desc,
      thumbnail: item.thumbnail,
      duration: "15:30",
      author: "张医生",
      authorAvatar: "/images/20.jpg",
      // 保留点击率相关字段供调试
      viewtimes_display: item.viewtimes_display,
      viewtimes: item.viewtimes,
      views: item.views,
    }));

    console.log('格式化后的数据:', formattedItems);

    // 更新列表数据
    items.value = formattedItems;
  } catch (err) {
    console.error('获取视频列表失败:', err);
    showToast('获取视频列表失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};
// 处理卡片点击
const handleItemClick = (item: any) => {
  console.log('点击的视频项:', item);
  router.push({ name: 'VideoDetail', params: { id: item.id } });
};
// 初始化
onMounted(() => {
  // 加载列表数据
  loadItems();
});
</script>

<style scoped>
/* 区块通用样式 */
.section-container {
  padding: 5px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
  color: #323233;
}

.section-title span {
  margin-left: 8px;
}

.more-btn {
  font-size: 12px;
}

/* 视频列表样式 */
.video-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  /* 2列布局 */
  gap: 12px;
  /* 调整间距 */
}

.video-card {
  border-radius: 12px;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  touch-action: manipulation;
  min-height: 200px;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.video-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.video-thumbnail {
  position: relative;
  height: 100px;
  overflow: hidden;
  flex-shrink: 0;
}

.video-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.video-card:hover .video-thumbnail img {
  transform: scale(1.05);
}

.video-duration {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;
}

.video-info {
  padding: 10px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.video-title {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 6px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  height: 40px;
  color: #323233;
  line-height: 1.3;
}

.video-author {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #969799;
}
</style>
