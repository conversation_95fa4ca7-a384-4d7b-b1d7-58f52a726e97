<template>
  <div class="section-container">
    <SectionHeader title="联系我们" icon="phone-o" :showMore="false" />
    
    <div class="contact-content">
      <!-- 联系信息列表 -->
      <div class="contact-list">
        <!-- 联系电话 -->
        <div class="contact-item" @click="makeCall">
          <div class="contact-icon phone-icon">
            <van-icon name="phone-o" size="20" />
          </div>
          <div class="contact-info">
            <div class="contact-label">联系电话</div>
            <div class="contact-value">{{ effectiveContactInfo.phone }}</div>
          </div>
        </div>

        <!-- 邮箱地址 -->
        <div class="contact-item" @click="sendEmail">
          <div class="contact-icon email-icon">
            <van-icon name="envelop-o" size="20" />
          </div>
          <div class="contact-info">
            <div class="contact-label">邮箱地址</div>
            <div class="contact-value">{{ effectiveContactInfo.email }}</div>
          </div>
        </div>

        <!-- 医院地址 -->
        <div class="contact-item" @click="openMap">
          <div class="contact-icon location-icon">
            <van-icon name="location-o" size="20" />
          </div>
          <div class="contact-info">
            <div class="contact-label">医院地址</div>
            <div class="contact-value">{{ effectiveContactInfo.address }}</div>
          </div>
        </div>

        <!-- 营业时间 -->
        <div class="contact-item">
          <div class="contact-icon time-icon">
            <van-icon name="clock-o" size="20" />
          </div>
          <div class="contact-info">
            <div class="contact-label">营业时间</div>
            <div class="contact-value">{{ effectiveContactInfo.business_hours }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { showToast } from 'vant';
import 'vant/es/toast/style';
import SectionHeader from '../../../components/SectionHeader.vue';
import { useSystemConfig } from '../../../composables/useSystemConfig';

// 使用系统配置
const { effectiveContactInfo } = useSystemConfig();

// 拨打电话
const makeCall = () => {
  const phoneNumber = effectiveContactInfo.value.phone;
  if (navigator.userAgent.match(/(iPhone|iPod|Android|ios)/i)) {
    window.location.href = `tel:${phoneNumber}`;
  } else {
    // 复制到剪贴板
    navigator.clipboard.writeText(phoneNumber).then(() => {
      showToast('电话号码已复制到剪贴板');
    }).catch(() => {
      showToast('复制失败，请手动拨打：' + phoneNumber);
    });
  }
};

// 发送邮件
const sendEmail = () => {
  const email = effectiveContactInfo.value.email;
  if (navigator.userAgent.match(/(iPhone|iPod|Android|ios)/i)) {
    window.location.href = `mailto:${email}`;
  } else {
    // 复制到剪贴板
    navigator.clipboard.writeText(email).then(() => {
      showToast('邮箱地址已复制到剪贴板');
    }).catch(() => {
      showToast('复制失败，邮箱：' + email);
    });
  }
};

// 打开地图
const openMap = () => {
  const address = effectiveContactInfo.value.address;
  // 尝试打开地图应用
  if (navigator.userAgent.match(/(iPhone|iPod|ios)/i)) {
    window.location.href = `maps://maps.apple.com/?q=${encodeURIComponent(address)}`;
  } else if (navigator.userAgent.match(/Android/i)) {
    window.location.href = `geo:0,0?q=${encodeURIComponent(address)}`;
  } else {
    // 复制地址到剪贴板
    navigator.clipboard.writeText(address).then(() => {
      showToast('地址已复制到剪贴板');
    }).catch(() => {
      showToast('地址：' + address);
    });
  }
};
</script>

<style scoped>
.contact-content {
  padding: 0;
}

.contact-list {
  margin-bottom: 3px; /* 从 16px 修改为 3px */
  background-color: #fafafa;
  border-radius: 8px;
  padding: 8px;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 18px 12px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 8px;
  margin-bottom: 4px;
}

.contact-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.contact-item:hover {
  background-color: #f8f9fa;
  transform: translateX(2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.contact-item:active {
  background-color: #f0f1f2;
  transform: translateX(1px);
}

.contact-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 14px;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.phone-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: #fff;
}

.email-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: #fff;
}

.location-icon {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  color: #fff;
}

.time-icon {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #333;
}

.contact-info {
  flex: 1;
  min-width: 0;
}

.contact-label {
  font-size: 13px;
  color: #666;
  margin-bottom: 3px;
  font-weight: 500;
}

.contact-value {
  font-size: 15px;
  color: #333;
  font-weight: 600;
  word-break: break-all;
  line-height: 1.3;
}

/* 响应式调整 */
@media (max-width: 480px) {
  .contact-item {
    padding: 8px 0; /* 从 14px 0 修改为 8px 0 */
  }
  
  .contact-icon {
    width: 40px;
    height: 40px;
    margin-right: 12px;
  }
  
  .contact-label {
    font-size: 13px;
  }
  
  .contact-value {
    font-size: 15px;
  }
}

@media (max-width: 320px) {
  .contact-item {
    padding: 12px 0;
  }
  
  .contact-icon {
    width: 36px;
    height: 36px;
    margin-right: 10px;
  }
  
  .contact-value {
    font-size: 14px;
  }
}
</style>
