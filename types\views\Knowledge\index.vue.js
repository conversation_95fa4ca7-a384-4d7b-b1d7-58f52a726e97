import { ref, onMounted } from "vue";
import { showToast } from "vant";
import { useRouter, useRoute } from "vue-router"; // 导入 useRouter 和 useRoute
import GlobalHeader from "../../components/GlobalHeader.vue"; // 导入通用页头组件
import GlobalFooter from "../../components/GlobalFooter.vue"; // 导入通用页脚组件
import Carousel from "../Home/components/Carousel.vue"; // 导入轮播组件
import ArticleGridSection from "../../components/ArticleGridSection.vue";
import Healthy from "./components/Healthy.vue";
import { getHomeDataList } from "./api";
const router = useRouter(); // 初始化 router
const route = useRoute(); // 初始化 route
// 响应式数据
const loading = ref(false);
const error = ref("");
const refreshing = ref(false);
// 功能菜单数据 - 参考首页实现
const firstRowItems = ref([
    {
        icon: "photo-o",
        text: "科教图文",
        bgColor: "#fff0f0",
        iconColor: "#ff6b35",
        route: "/education-media",
        routeName: "EducationMedia",
    },
    {
        icon: "question-o",
        text: "中医知识",
        bgColor: "#e8f4ff",
        iconColor: "#4b8bf4",
        route: "/tcm-knowledge",
        routeName: "TcmKnowledge",
    },
]);
const secondRowItems = ref([
    {
        icon: "star-o",
        text: "中医文化",
        bgColor: "#f0e6ff",
        iconColor: "#7c3aed",
        route: "/culture",
        routeName: "Culture",
    },
    {
        icon: "records",
        text: "中医案例",
        bgColor: "#e6f7ef",
        iconColor: "#059669",
        route: "/cases",
        routeName: "Cases",
    },
    {
        icon: "video-o",
        text: "视频宣传",
        bgColor: "#ffebe6",
        iconColor: "#ff8c69",
        route: "/video",
        routeName: "Video",
    },
]);
const homeData = ref({
    culture_best_data: [],
    knowledge_best_data: [],
    case_best_data: [],
    culture_healthy_data: [],
    knowledge_healthy_data: [],
    case_healthy_data: [],
});
// 获取文章列表
const fetchArticles = async () => {
    if (loading.value)
        return;
    try {
        loading.value = true;
        error.value = "";
        const res = await getHomeDataList({ source: 'knowledge' });
        homeData.value = res.items;
        console.log("获取到的文章列表数据:", homeData.value);
    }
    catch (err) {
        console.error("获取文章列表失败:", err);
        error.value = "获取文章列表失败，请稍后再试";
    }
    finally {
        loading.value = false;
        refreshing.value = false;
    }
};
// 判断是否是当前激活的路由
const isActiveRoute = (item) => {
    return route.name === item.routeName;
};
// 获取图标颜色
const getIconColor = (item) => {
    return isActiveRoute(item) ? '#fff' : item.iconColor;
};
// 获取图标样式
const getIconStyle = (item) => {
    if (isActiveRoute(item)) {
        return {
            background: `linear-gradient(135deg, ${item.iconColor}, ${item.iconColor}dd)`
        };
    }
    return {
        backgroundColor: item.bgColor
    };
};
const onFunctionClick = (item) => {
    console.log(`点击了功能项: ${item.text}, 路由路径: ${item.route}`);
    // 根据功能项跳转到对应的分类页面
    if (item.route) {
        // 使用 router.push 进行路由跳转
        router.push(item.route).catch((err) => {
            console.error("路由跳转失败:", err);
            showToast(`跳转到${item.text}失败，请稍后再试`);
        });
    }
    else {
        showToast(`${item.text}功能开发中`);
    }
};
onMounted(() => {
    fetchArticles();
});
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "knowledge-container" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.meta)({
    name: "viewport",
    content: "width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no",
});
/** @type {[typeof GlobalHeader, ]} */ ;
// @ts-ignore
const __VLS_0 = __VLS_asFunctionalComponent(GlobalHeader, new GlobalHeader({
    title: "中医知识",
}));
const __VLS_1 = __VLS_0({
    title: "中医知识",
}, ...__VLS_functionalComponentArgsRest(__VLS_0));
/** @type {[typeof Carousel, ]} */ ;
// @ts-ignore
const __VLS_3 = __VLS_asFunctionalComponent(Carousel, new Carousel({
    position: "2",
}));
const __VLS_4 = __VLS_3({
    position: "2",
}, ...__VLS_functionalComponentArgsRest(__VLS_3));
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "section-container" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "section-header" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "section-title" },
});
const __VLS_6 = {}.VanIcon;
/** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
// @ts-ignore
const __VLS_7 = __VLS_asFunctionalComponent(__VLS_6, new __VLS_6({
    name: "apps-o",
    color: "#4b8bf4",
    size: "18",
}));
const __VLS_8 = __VLS_7({
    name: "apps-o",
    color: "#4b8bf4",
    size: "18",
}, ...__VLS_functionalComponentArgsRest(__VLS_7));
__VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({});
const __VLS_10 = {}.VanGrid;
/** @type {[typeof __VLS_components.VanGrid, typeof __VLS_components.vanGrid, typeof __VLS_components.VanGrid, typeof __VLS_components.vanGrid, ]} */ ;
// @ts-ignore
const __VLS_11 = __VLS_asFunctionalComponent(__VLS_10, new __VLS_10({
    columnNum: (2),
    border: (false),
    gutter: (10),
    ...{ class: "function-grid first-row" },
}));
const __VLS_12 = __VLS_11({
    columnNum: (2),
    border: (false),
    gutter: (10),
    ...{ class: "function-grid first-row" },
}, ...__VLS_functionalComponentArgsRest(__VLS_11));
__VLS_13.slots.default;
for (const [item, index] of __VLS_getVForSourceType((__VLS_ctx.firstRowItems))) {
    const __VLS_14 = {}.VanGridItem;
    /** @type {[typeof __VLS_components.VanGridItem, typeof __VLS_components.vanGridItem, typeof __VLS_components.VanGridItem, typeof __VLS_components.vanGridItem, ]} */ ;
    // @ts-ignore
    const __VLS_15 = __VLS_asFunctionalComponent(__VLS_14, new __VLS_14({
        key: (index),
        ...{ class: "animate__animated animate__fadeInUp" },
        ...{ style: ({ animationDelay: index * 0.05 + 's' }) },
    }));
    const __VLS_16 = __VLS_15({
        key: (index),
        ...{ class: "animate__animated animate__fadeInUp" },
        ...{ style: ({ animationDelay: index * 0.05 + 's' }) },
    }, ...__VLS_functionalComponentArgsRest(__VLS_15));
    __VLS_17.slots.default;
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ onClick: (...[$event]) => {
                __VLS_ctx.onFunctionClick(item);
            } },
        ...{ class: "grid-item-content" },
        ...{ class: ({ active: __VLS_ctx.isActiveRoute(item) }) },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "icon-wrapper" },
        ...{ class: ({ active: __VLS_ctx.isActiveRoute(item) }) },
        ...{ style: (__VLS_ctx.getIconStyle(item)) },
    });
    const __VLS_18 = {}.VanIcon;
    /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
    // @ts-ignore
    const __VLS_19 = __VLS_asFunctionalComponent(__VLS_18, new __VLS_18({
        name: (item.icon),
        color: (__VLS_ctx.getIconColor(item)),
        size: "24",
    }));
    const __VLS_20 = __VLS_19({
        name: (item.icon),
        color: (__VLS_ctx.getIconColor(item)),
        size: "24",
    }, ...__VLS_functionalComponentArgsRest(__VLS_19));
    __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
        ...{ class: "grid-text" },
        ...{ class: ({ active: __VLS_ctx.isActiveRoute(item) }) },
    });
    (item.text);
    var __VLS_17;
}
var __VLS_13;
const __VLS_22 = {}.VanGrid;
/** @type {[typeof __VLS_components.VanGrid, typeof __VLS_components.vanGrid, typeof __VLS_components.VanGrid, typeof __VLS_components.vanGrid, ]} */ ;
// @ts-ignore
const __VLS_23 = __VLS_asFunctionalComponent(__VLS_22, new __VLS_22({
    columnNum: (3),
    border: (false),
    gutter: (10),
    ...{ class: "function-grid second-row" },
}));
const __VLS_24 = __VLS_23({
    columnNum: (3),
    border: (false),
    gutter: (10),
    ...{ class: "function-grid second-row" },
}, ...__VLS_functionalComponentArgsRest(__VLS_23));
__VLS_25.slots.default;
for (const [item, index] of __VLS_getVForSourceType((__VLS_ctx.secondRowItems))) {
    const __VLS_26 = {}.VanGridItem;
    /** @type {[typeof __VLS_components.VanGridItem, typeof __VLS_components.vanGridItem, typeof __VLS_components.VanGridItem, typeof __VLS_components.vanGridItem, ]} */ ;
    // @ts-ignore
    const __VLS_27 = __VLS_asFunctionalComponent(__VLS_26, new __VLS_26({
        key: (index),
        ...{ class: "animate__animated animate__fadeInUp" },
        ...{ style: ({ animationDelay: (index + 2) * 0.05 + 's' }) },
    }));
    const __VLS_28 = __VLS_27({
        key: (index),
        ...{ class: "animate__animated animate__fadeInUp" },
        ...{ style: ({ animationDelay: (index + 2) * 0.05 + 's' }) },
    }, ...__VLS_functionalComponentArgsRest(__VLS_27));
    __VLS_29.slots.default;
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ onClick: (...[$event]) => {
                __VLS_ctx.onFunctionClick(item);
            } },
        ...{ class: "grid-item-content" },
        ...{ class: ({ active: __VLS_ctx.isActiveRoute(item) }) },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "icon-wrapper" },
        ...{ class: ({ active: __VLS_ctx.isActiveRoute(item) }) },
        ...{ style: (__VLS_ctx.getIconStyle(item)) },
    });
    const __VLS_30 = {}.VanIcon;
    /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
    // @ts-ignore
    const __VLS_31 = __VLS_asFunctionalComponent(__VLS_30, new __VLS_30({
        name: (item.icon),
        color: (__VLS_ctx.getIconColor(item)),
        size: "24",
    }));
    const __VLS_32 = __VLS_31({
        name: (item.icon),
        color: (__VLS_ctx.getIconColor(item)),
        size: "24",
    }, ...__VLS_functionalComponentArgsRest(__VLS_31));
    __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
        ...{ class: "grid-text" },
        ...{ class: ({ active: __VLS_ctx.isActiveRoute(item) }) },
    });
    (item.text);
    var __VLS_29;
}
var __VLS_25;
/** @type {[typeof ArticleGridSection, ]} */ ;
// @ts-ignore
const __VLS_34 = __VLS_asFunctionalComponent(ArticleGridSection, new ArticleGridSection({
    title: "精品文章",
    icon: "description-o",
    showMore: (false),
    cultureData: (__VLS_ctx.homeData.culture_best_data),
    knowledgeData: (__VLS_ctx.homeData.knowledge_best_data),
    caseData: (__VLS_ctx.homeData.case_best_data),
}));
const __VLS_35 = __VLS_34({
    title: "精品文章",
    icon: "description-o",
    showMore: (false),
    cultureData: (__VLS_ctx.homeData.culture_best_data),
    knowledgeData: (__VLS_ctx.homeData.knowledge_best_data),
    caseData: (__VLS_ctx.homeData.case_best_data),
}, ...__VLS_functionalComponentArgsRest(__VLS_34));
/** @type {[typeof Healthy, ]} */ ;
// @ts-ignore
const __VLS_37 = __VLS_asFunctionalComponent(Healthy, new Healthy({
    cultureHealthyData: (__VLS_ctx.homeData.culture_healthy_data),
    knowledgeHealthyData: (__VLS_ctx.homeData.knowledge_healthy_data),
    caseHealthyData: (__VLS_ctx.homeData.case_healthy_data),
}));
const __VLS_38 = __VLS_37({
    cultureHealthyData: (__VLS_ctx.homeData.culture_healthy_data),
    knowledgeHealthyData: (__VLS_ctx.homeData.knowledge_healthy_data),
    caseHealthyData: (__VLS_ctx.homeData.case_healthy_data),
}, ...__VLS_functionalComponentArgsRest(__VLS_37));
if (__VLS_ctx.loading) {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "loading-container" },
    });
    const __VLS_40 = {}.VanLoading;
    /** @type {[typeof __VLS_components.VanLoading, typeof __VLS_components.vanLoading, ]} */ ;
    // @ts-ignore
    const __VLS_41 = __VLS_asFunctionalComponent(__VLS_40, new __VLS_40({
        type: "spinner",
        color: "#1989fa",
    }));
    const __VLS_42 = __VLS_41({
        type: "spinner",
        color: "#1989fa",
    }, ...__VLS_functionalComponentArgsRest(__VLS_41));
    __VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({});
}
const __VLS_44 = {}.VanBackTop;
/** @type {[typeof __VLS_components.VanBackTop, typeof __VLS_components.vanBackTop, ]} */ ;
// @ts-ignore
const __VLS_45 = __VLS_asFunctionalComponent(__VLS_44, new __VLS_44({
    right: "16",
    bottom: "80",
}));
const __VLS_46 = __VLS_45({
    right: "16",
    bottom: "80",
}, ...__VLS_functionalComponentArgsRest(__VLS_45));
/** @type {[typeof GlobalFooter, ]} */ ;
// @ts-ignore
const __VLS_48 = __VLS_asFunctionalComponent(GlobalFooter, new GlobalFooter({}));
const __VLS_49 = __VLS_48({}, ...__VLS_functionalComponentArgsRest(__VLS_48));
/** @type {__VLS_StyleScopedClasses['knowledge-container']} */ ;
/** @type {__VLS_StyleScopedClasses['section-container']} */ ;
/** @type {__VLS_StyleScopedClasses['section-header']} */ ;
/** @type {__VLS_StyleScopedClasses['section-title']} */ ;
/** @type {__VLS_StyleScopedClasses['function-grid']} */ ;
/** @type {__VLS_StyleScopedClasses['first-row']} */ ;
/** @type {__VLS_StyleScopedClasses['animate__animated']} */ ;
/** @type {__VLS_StyleScopedClasses['animate__fadeInUp']} */ ;
/** @type {__VLS_StyleScopedClasses['grid-item-content']} */ ;
/** @type {__VLS_StyleScopedClasses['active']} */ ;
/** @type {__VLS_StyleScopedClasses['icon-wrapper']} */ ;
/** @type {__VLS_StyleScopedClasses['active']} */ ;
/** @type {__VLS_StyleScopedClasses['grid-text']} */ ;
/** @type {__VLS_StyleScopedClasses['active']} */ ;
/** @type {__VLS_StyleScopedClasses['function-grid']} */ ;
/** @type {__VLS_StyleScopedClasses['second-row']} */ ;
/** @type {__VLS_StyleScopedClasses['animate__animated']} */ ;
/** @type {__VLS_StyleScopedClasses['animate__fadeInUp']} */ ;
/** @type {__VLS_StyleScopedClasses['grid-item-content']} */ ;
/** @type {__VLS_StyleScopedClasses['active']} */ ;
/** @type {__VLS_StyleScopedClasses['icon-wrapper']} */ ;
/** @type {__VLS_StyleScopedClasses['active']} */ ;
/** @type {__VLS_StyleScopedClasses['grid-text']} */ ;
/** @type {__VLS_StyleScopedClasses['active']} */ ;
/** @type {__VLS_StyleScopedClasses['loading-container']} */ ;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            GlobalHeader: GlobalHeader,
            GlobalFooter: GlobalFooter,
            Carousel: Carousel,
            ArticleGridSection: ArticleGridSection,
            Healthy: Healthy,
            loading: loading,
            firstRowItems: firstRowItems,
            secondRowItems: secondRowItems,
            homeData: homeData,
            isActiveRoute: isActiveRoute,
            getIconColor: getIconColor,
            getIconStyle: getIconStyle,
            onFunctionClick: onFunctionClick,
        };
    },
});
export default (await import('vue')).defineComponent({
    setup() {
        return {};
    },
});
; /* PartiallyEnd: #4569/main.vue */
