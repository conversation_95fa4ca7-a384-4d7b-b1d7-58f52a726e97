import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import SectionHeader from './SectionHeader.vue';
import GridCard from './GridCard.vue';
import { transformArticleListToGridCard, getCategoryRouteName, getCategoryBadge } from '../utils/articleTransform';
const router = useRouter();
const loading = ref(false);
const props = withDefaults(defineProps(), {
    title: '精品文章',
    icon: 'description-o',
    showMore: false
});
// 转换数据为GridCard格式
const cultureGridItems = computed(() => {
    return transformArticleListToGridCard(props.cultureData, getCategoryBadge('culture'));
});
const knowledgeGridItems = computed(() => {
    return transformArticleListToGridCard(props.knowledgeData, getCategoryBadge('knowledge'));
});
const caseGridItems = computed(() => {
    return transformArticleListToGridCard(props.caseData, getCategoryBadge('case'));
});
// 检查是否有文章
const hasArticles = computed(() => {
    return cultureGridItems.value.length > 0 ||
        knowledgeGridItems.value.length > 0 ||
        caseGridItems.value.length > 0;
});
// 处理卡片点击 - 根据原始数据判断分类
const handleCardClick = (item) => {
    console.log('点击了文章:', item);
    // 从原始数据中获取分类信息
    const originalData = item.originalData;
    // 根据数据来源确定分类和路由
    let category = '';
    if (props.cultureData.some(culture => culture.id === item.id)) {
        category = 'culture';
    }
    else if (props.knowledgeData.some(knowledge => knowledge.id === item.id)) {
        category = 'knowledge';
    }
    else if (props.caseData.some(caseItem => caseItem.id === item.id)) {
        category = 'case';
    }
    // 获取对应的路由名称
    const routeName = getCategoryRouteName(category);
    // 跳转到详情页
    router.push({ name: routeName, params: { id: item.id } });
};
// 组件挂载时打印数据（保持与原组件一致）
onMounted(() => {
    console.log(`${props.title} - cultureData:`, props.cultureData);
    console.log(`${props.title} - knowledgeData:`, props.knowledgeData);
    console.log(`${props.title} - caseData:`, props.caseData);
    console.log(`${props.title} - 转换后的GridCard数据:`);
    console.log('- culture:', cultureGridItems.value);
    console.log('- knowledge:', knowledgeGridItems.value);
    console.log('- case:', caseGridItems.value);
});
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_withDefaultsArg = (function (t) { return t; })({
    title: '精品文章',
    icon: 'description-o',
    showMore: false
});
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
/** @type {__VLS_StyleScopedClasses['articles-container']} */ ;
/** @type {__VLS_StyleScopedClasses['category-section']} */ ;
/** @type {__VLS_StyleScopedClasses['category-title']} */ ;
/** @type {__VLS_StyleScopedClasses['articles-container']} */ ;
/** @type {__VLS_StyleScopedClasses['category-section']} */ ;
/** @type {__VLS_StyleScopedClasses['category-title']} */ ;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "section-container featured-articles-section" },
});
/** @type {[typeof SectionHeader, ]} */ ;
// @ts-ignore
const __VLS_0 = __VLS_asFunctionalComponent(SectionHeader, new SectionHeader({
    title: (__VLS_ctx.title),
    icon: (__VLS_ctx.icon),
    showMore: (__VLS_ctx.showMore),
}));
const __VLS_1 = __VLS_0({
    title: (__VLS_ctx.title),
    icon: (__VLS_ctx.icon),
    showMore: (__VLS_ctx.showMore),
}, ...__VLS_functionalComponentArgsRest(__VLS_0));
if (__VLS_ctx.loading) {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "loading-container" },
    });
    const __VLS_3 = {}.VanLoading;
    /** @type {[typeof __VLS_components.VanLoading, typeof __VLS_components.vanLoading, typeof __VLS_components.VanLoading, typeof __VLS_components.vanLoading, ]} */ ;
    // @ts-ignore
    const __VLS_4 = __VLS_asFunctionalComponent(__VLS_3, new __VLS_3({
        size: "24px",
    }));
    const __VLS_5 = __VLS_4({
        size: "24px",
    }, ...__VLS_functionalComponentArgsRest(__VLS_4));
    __VLS_6.slots.default;
    var __VLS_6;
}
else if (!__VLS_ctx.hasArticles) {
    const __VLS_7 = {}.VanEmpty;
    /** @type {[typeof __VLS_components.VanEmpty, typeof __VLS_components.vanEmpty, ]} */ ;
    // @ts-ignore
    const __VLS_8 = __VLS_asFunctionalComponent(__VLS_7, new __VLS_7({
        description: (`暂无${__VLS_ctx.title}`),
    }));
    const __VLS_9 = __VLS_8({
        description: (`暂无${__VLS_ctx.title}`),
    }, ...__VLS_functionalComponentArgsRest(__VLS_8));
}
else {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "articles-container" },
    });
    if (__VLS_ctx.cultureGridItems.length > 0) {
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "category-section" },
        });
        /** @type {[typeof GridCard, ]} */ ;
        // @ts-ignore
        const __VLS_11 = __VLS_asFunctionalComponent(GridCard, new GridCard({
            ...{ 'onCardClick': {} },
            items: (__VLS_ctx.cultureGridItems),
        }));
        const __VLS_12 = __VLS_11({
            ...{ 'onCardClick': {} },
            items: (__VLS_ctx.cultureGridItems),
        }, ...__VLS_functionalComponentArgsRest(__VLS_11));
        let __VLS_14;
        let __VLS_15;
        let __VLS_16;
        const __VLS_17 = {
            onCardClick: (__VLS_ctx.handleCardClick)
        };
        var __VLS_13;
    }
    if (__VLS_ctx.knowledgeGridItems.length > 0) {
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "category-section" },
        });
        /** @type {[typeof GridCard, ]} */ ;
        // @ts-ignore
        const __VLS_18 = __VLS_asFunctionalComponent(GridCard, new GridCard({
            ...{ 'onCardClick': {} },
            items: (__VLS_ctx.knowledgeGridItems),
        }));
        const __VLS_19 = __VLS_18({
            ...{ 'onCardClick': {} },
            items: (__VLS_ctx.knowledgeGridItems),
        }, ...__VLS_functionalComponentArgsRest(__VLS_18));
        let __VLS_21;
        let __VLS_22;
        let __VLS_23;
        const __VLS_24 = {
            onCardClick: (__VLS_ctx.handleCardClick)
        };
        var __VLS_20;
    }
    if (__VLS_ctx.caseGridItems.length > 0) {
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "category-section" },
        });
        /** @type {[typeof GridCard, ]} */ ;
        // @ts-ignore
        const __VLS_25 = __VLS_asFunctionalComponent(GridCard, new GridCard({
            ...{ 'onCardClick': {} },
            items: (__VLS_ctx.caseGridItems),
        }));
        const __VLS_26 = __VLS_25({
            ...{ 'onCardClick': {} },
            items: (__VLS_ctx.caseGridItems),
        }, ...__VLS_functionalComponentArgsRest(__VLS_25));
        let __VLS_28;
        let __VLS_29;
        let __VLS_30;
        const __VLS_31 = {
            onCardClick: (__VLS_ctx.handleCardClick)
        };
        var __VLS_27;
    }
}
/** @type {__VLS_StyleScopedClasses['section-container']} */ ;
/** @type {__VLS_StyleScopedClasses['featured-articles-section']} */ ;
/** @type {__VLS_StyleScopedClasses['loading-container']} */ ;
/** @type {__VLS_StyleScopedClasses['articles-container']} */ ;
/** @type {__VLS_StyleScopedClasses['category-section']} */ ;
/** @type {__VLS_StyleScopedClasses['category-section']} */ ;
/** @type {__VLS_StyleScopedClasses['category-section']} */ ;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            SectionHeader: SectionHeader,
            GridCard: GridCard,
            loading: loading,
            cultureGridItems: cultureGridItems,
            knowledgeGridItems: knowledgeGridItems,
            caseGridItems: caseGridItems,
            hasArticles: hasArticles,
            handleCardClick: handleCardClick,
        };
    },
    __typeProps: {},
    props: {},
});
export default (await import('vue')).defineComponent({
    setup() {
        return {};
    },
    __typeProps: {},
    props: {},
});
; /* PartiallyEnd: #4569/main.vue */
