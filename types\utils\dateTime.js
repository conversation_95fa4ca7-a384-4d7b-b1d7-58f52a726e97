/**
 * 时间格式化工具函数
 * 智能处理多种时间格式，包括：
 * - 标准格式：YYYY-MM-DD HH:mm:ss
 * - 不带年份：MM-DD HH:mm 或 MM/DD HH:mm
 * - 中文格式：12月25日 14:30 或 12月25日
 * - 纯日期：MM-DD 或 MM/DD
 *
 * 对于不带年份的格式，自动使用当前年份补全
 */
/**
 * 智能解析时间字符串，支持多种格式
 * @param datetimeStr 时间字符串，支持多种格式
 * @returns Date对象，解析失败返回null
 */
const parseDateTime = (datetimeStr) => {
    if (!datetimeStr)
        return null;
    const currentYear = new Date().getFullYear();
    // 优先处理格式：MM/DD HH:mm 或 MM/DD HH:mm:ss
    const matchWithSlashAndTime = datetimeStr.match(/^(\d{1,2})\/(\d{1,2})\s+(\d{1,2}):(\d{1,2})(:(\d{1,2}))?$/);
    if (matchWithSlashAndTime) {
        const month = parseInt(matchWithSlashAndTime[1], 10) - 1;
        const day = parseInt(matchWithSlashAndTime[2], 10);
        const hour = parseInt(matchWithSlashAndTime[3], 10);
        const minute = parseInt(matchWithSlashAndTime[4], 10);
        const second = matchWithSlashAndTime[6] ? parseInt(matchWithSlashAndTime[6], 10) : 0;
        return new Date(currentYear, month, day, hour, minute, second);
    }
    // 处理格式：MM-DD HH:mm 或 MM-DD HH:mm:ss
    const matchWithDashAndTime = datetimeStr.match(/^(\d{1,2})-(\d{1,2})\s+(\d{1,2}):(\d{1,2})(:(\d{1,2}))?$/);
    if (matchWithDashAndTime) {
        const month = parseInt(matchWithDashAndTime[1], 10) - 1;
        const day = parseInt(matchWithDashAndTime[2], 10);
        const hour = parseInt(matchWithDashAndTime[3], 10);
        const minute = parseInt(matchWithDashAndTime[4], 10);
        const second = matchWithDashAndTime[6] ? parseInt(matchWithDashAndTime[6], 10) : 0;
        return new Date(currentYear, month, day, hour, minute, second);
    }
    // 处理中文格式：12月25日 14:30
    const matchChineseWithTime = datetimeStr.match(/^(\d{1,2})月(\d{1,2})日\s+(\d{1,2}):(\d{1,2})$/);
    if (matchChineseWithTime) {
        const [, month, day, hour, minute] = matchChineseWithTime;
        return new Date(currentYear, parseInt(month) - 1, parseInt(day), parseInt(hour), parseInt(minute));
    }
    // 处理只有日期的格式：MM-DD 或 MM/DD
    const matchDateOnly = datetimeStr.match(/^(\d{1,2})[-\/](\d{1,2})$/);
    if (matchDateOnly) {
        const month = parseInt(matchDateOnly[1], 10) - 1;
        const day = parseInt(matchDateOnly[2], 10);
        return new Date(currentYear, month, day);
    }
    // 处理中文日期格式：12月25日
    const matchChineseDateOnly = datetimeStr.match(/^(\d{1,2})月(\d{1,2})日$/);
    if (matchChineseDateOnly) {
        const [, month, day] = matchChineseDateOnly;
        return new Date(currentYear, parseInt(month) - 1, parseInt(day));
    }
    // 最后尝试直接解析，适用于 YYYY-MM-DD HH:mm:ss 等完整格式
    const date = new Date(datetimeStr);
    if (!isNaN(date.getTime())) {
        return date;
    }
    return null;
};
/**
 * 格式化datetime字符串为友好的显示格式
 * @param datetimeStr datetime字符串，支持多种格式
 * @param options 格式化选项
 * @returns 格式化后的时间字符串
 */
export const formatDateTime = (datetimeStr, options = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
}) => {
    if (!datetimeStr)
        return '';
    try {
        const date = parseDateTime(datetimeStr);
        if (!date)
            return datetimeStr; // 如果解析失败，返回原字符串
        return date.toLocaleDateString('zh-CN', options);
    }
    catch (error) {
        return datetimeStr; // 解析出错时返回原字符串
    }
};
/**
 * 格式化活动时间范围
 * @param startDate 开始时间 (datetime格式)
 * @param endDate 结束时间 (datetime格式)
 * @returns 格式化后的时间范围字符串
 */
export const formatActivityTimeRange = (startDate, endDate) => {
    if (!startDate)
        return '时间待定';
    const startDateObj = parseDateTime(startDate);
    const endDateObj = endDate ? parseDateTime(endDate) : null;
    // 检查日期是否有效
    if (!startDateObj) {
        return '日期格式错误';
    }
    if (!endDate || !endDateObj || startDateObj.getTime() === endDateObj.getTime()) {
        return formatDateTime(startDate, {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
    // 检查是否是同一天
    if (startDateObj.toDateString() === endDateObj.toDateString()) {
        // 同一天，只显示日期和时间范围
        const dateStr = startDateObj.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
        const startTime = startDateObj.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });
        const endTime = endDateObj.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });
        return `${dateStr} ${startTime}-${endTime}`;
    }
    else {
        // 不同天，显示完整的开始和结束时间
        const startFormatted = formatDateTime(startDate);
        const endFormatted = formatDateTime(endDate);
        return `${startFormatted} ~ ${endFormatted}`;
    }
};
/**
 * 格式化报名截止时间
 * @param deadline 截止时间 (datetime格式)
 * @returns 格式化后的截止时间字符串
 */
export const formatDeadline = (deadline) => {
    if (!deadline)
        return '暂无数据';
    // 对于不带年份的格式，只显示月日和时间
    const match = deadline.match(/^\d{1,2}[-\/]\d{1,2}\s+\d{1,2}:\d{1,2}(:\d{1,2})?$/);
    if (match) {
        return formatDateTime(deadline, {
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
    return formatDateTime(deadline, {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
};
