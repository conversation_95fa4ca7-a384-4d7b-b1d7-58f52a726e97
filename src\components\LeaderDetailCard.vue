<template>
  <div class="leader-detail-card">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <van-loading type="spinner" color="#1989fa" size="24" />
      <p class="loading-text">加载中...</p>
    </div>

    <!-- 错误提示 -->
    <div v-else-if="error" class="error-container">
      <van-empty description="加载失败" image="error">
        <template #description>
          <p class="error-text">{{ error }}</p>
        </template>
        <van-button round type="primary" @click="handleRetry" class="retry-btn">
          重试
        </van-button>
      </van-empty>
    </div>

    <!-- 领导详情内容 -->
    <div v-else class="content">
      <!-- 第1部分：缩略图区域 -->
      <div class="thumbnail-section">
        <div class="section-header">
          <van-icon name="photo-o" size="18" color="#4b8bf4" />
          <h2 class="section-title">{{ leaderData.name }}</h2>
        </div>
        <div class="thumbnail-content">
          <div class="large-avatar">
            <img 
              :src="leaderData.thumbnail || defaultAvatar" 
              :alt="leaderData.name"
              @error="handleImageError"
            />
            <div class="avatar-overlay">
              <van-icon name="user-o" size="32" color="#fff" />
            </div>
          </div>
        </div>
      </div>
      
      <!-- 第2部分：统一的信息区域 -->
      <div class="info-section">
        <!-- 时间和浏览次数 -->
        <div class="meta-container">
          <div class="meta-item" v-if="leaderData.create_time && leaderData.create_time.trim()">
            <van-icon name="clock-o" size="14" color="#999" />
            <span class="meta-text">发布时间：{{ formatTime(leaderData.create_time) }}</span>
          </div>
          <div class="meta-item">
            <van-icon name="eye-o" size="14" color="#999" />
            <span class="meta-text">浏览次数：{{ leaderData.viewtimes_display || '0' }}</span>
          </div>
        </div>
        
        <!-- 标签区域 -->
        <div class="tags-container">
          <van-tag 
            v-if="leaderData.job && leaderData.job.trim()" 
            size="large" 
            round 
            class="tag-item tag-blue"
          >
            {{ leaderData.job }}
          </van-tag>

          <!-- 如果有额外标签，也显示出来 -->
          <van-tag 
            v-for="(tag, index) in leaderData.tags" 
            :key="index"
            size="large"
            round
            :class="`tag-item ${getTagColorClass(index)}`"
          >
            {{ tag }}
          </van-tag>
        </div>
      </div>

      <!-- 第4部分：描述信息区域 -->
      <div class="description-section" v-if="leaderData.desc">
        <div class="section-header">
          <van-icon name="info-o" size="18" color="#f5a623" />
          <h2 class="section-title">基本信息</h2>
        </div>
        <div class="section-content">
          <div class="desc-content" v-html="formatDescription(leaderData.desc)"></div>
        </div>
      </div>

      <!-- 第5部分：富文本区域 -->
      <div class="richtext-section" v-if="leaderData.content">
        <div class="section-header">
          <van-icon name="notes-o" size="18" color="#722ed1" />
          <h2 class="section-title">详细介绍</h2>
        </div>
        <div class="section-content">
          <div class="rich-content" v-html="leaderData.content"></div>
        </div>
      </div>

      <!-- 第6部分：返回按钮区域 -->
      <div class="back-button-section">
        <van-button 
          type="primary" 
          size="large" 
          round 
          block
          icon="arrow-left"
          @click="handleGoBack"
          class="back-button"
        >
          返回上一级菜单
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { showToast } from 'vant';
import { useRouter } from 'vue-router';
import { getLeaderDetail } from '../views/LeaderDetail/api';
import { getDoctorDetail } from '../views/DoctorDetail/api';
import { getMedicineDetail } from '../views/MedicineDetail/api';
import { getDeptDetail } from '../views/DeptDetail/api';
import { getMainDeptDetail } from '../views/MainDeptDetail/api';
import { getEducationDetail } from '../views/EducationMediaDetail/api';
import { getKnowledgeDetail } from '../views/TcmKnowledgeDetail/api';
import { getCultureDetail } from '../views/CultureDetail/api';
import { getCaseDetail } from '../views/CasesDetail/api';
import type { LeaderDetail } from '../views/LeaderDetail/api';

// 组件属性定义
interface Props {
  leaderId?: string | number;  // 领导ID
  leaderInfo?: LeaderDetail;   // 直接传入的领导信息
  showRetryButton?: boolean;   // 是否显示重试按钮
  apiType?: 'leader' | 'doctor' | 'medicine' | 'dept' | 'mainDept' | 'education' | 'knowledge' | 'culture' | 'case';  // API类型
}

const props = withDefaults(defineProps<Props>(), {
  leaderId: '',
  leaderInfo: undefined,
  showRetryButton: true,
  apiType: 'leader'
});

// 组件事件定义
const emit = defineEmits<{
  retry: [];
  loaded: [data: LeaderDetail];
  error: [error: string];
  goBack: [];
}>();

// 路由实例
const router = useRouter();

// 响应式数据
const loading = ref(false);
const error = ref('');
const defaultAvatar = '/images/default-avatar.svg';

// 领导数据
const leaderData = ref<LeaderDetail>({
  id: 0,
  name: '',
  job: '',
  desc: '',
  thumbnail: '',
  cat_display: '',
  tags: [],
  content: '',
  create_time: '',
  specialties: [],
  experience: [],
  publications: [],
  achievements: [],
  research_directions: []
});

// 计算属性：确定数据源
const dataSource = computed(() => {
  return props.leaderInfo || leaderData.value;
});

// 获取详情数据
const fetchLeaderDetail = async () => {
  if (!props.leaderId) {
    error.value = 'ID无效';
    return;
  }
  
  try {
    loading.value = true;
    error.value = '';
    
    let response: any;
    let data: any;
    
    // 根据API类型调用不同的API
    switch (props.apiType) {
      case 'doctor':
        response = await getDoctorDetail(props.leaderId);
        data = response.famousdoctor;
        break;
      case 'medicine':
        response = await getMedicineDetail(props.leaderId);
        data = response.specialpreparations;
        break;
      case 'dept':
        response = await getDeptDetail(props.leaderId);
        data = response.characterdepart;
        break;
      case 'mainDept':
        response = await getMainDeptDetail(props.leaderId);
        data = response.maindept;
        break;
      case 'education':
        response = await getEducationDetail(props.leaderId);
        data = response.drumbeating;
        break;
      case 'knowledge':
        response = await getKnowledgeDetail(props.leaderId);
        data = response.knowledge;
        break;
      case 'culture':
        response = await getCultureDetail(props.leaderId);
        data = response.culture;
        break;
      case 'case':
        response = await getCaseDetail(props.leaderId);
        data = response.case;
        break;
      case 'leader':
      default:
        response = await getLeaderDetail(props.leaderId);
        data = response.leader;
        break;
    }
    
    if (!data) {
      throw new Error('API返回数据格式不正确');
    }
    
    const thumbnail = data.thumbnail ? data.thumbnail.trim() : '';
    
    leaderData.value = {
      id: data.id,
      name: data.name || '未知',
      job: data.job || '',
      desc: data.desc || '',
      thumbnail: thumbnail,
      cat_display: data.cat_display || data.category_name || '未分类',
      tags: data.tags || [],
      content: data.content || '',
      create_time: data.create_time || new Date().toISOString(), // 如果API没有返回时间，使用当前时间
      viewtimes_display: data.viewtimes_display || '0', // 浏览次数显示
      specialties: [],
      experience: [],
      publications: [],
      achievements: [],
      research_directions: []
    };
    
    // 调试信息：检查create_time字段
    console.log('API返回的原始数据:', data);
    console.log('create_time字段值:', data.create_time);
    console.log('处理后的leaderData:', leaderData.value);
    
    emit('loaded', leaderData.value);
  } catch (err) {
    console.error('获取详情失败:', err);
    error.value = '获取详情失败，请稍后再试';
    emit('error', error.value);
  } finally {
    loading.value = false;
  }
};

// 监听leaderId变化
watch(
  () => props.leaderId,
  (newId) => {
    if (newId && !props.leaderInfo) {
      fetchLeaderDetail();
    }
  },
  { immediate: true }
);

// 监听leaderInfo变化
watch(
  () => props.leaderInfo,
  (newInfo) => {
    if (newInfo) {
      leaderData.value = { ...newInfo };
    }
  },
  { immediate: true, deep: true }
);

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement;
  img.src = defaultAvatar;
};

// 格式化描述
const formatDescription = (desc: string) => {
  return desc.replace(/\r\n/g, '<br>').replace(/\n/g, '<br>');
};

// 格式化时间
const formatTime = (timeStr: string) => {
  if (!timeStr) return '';
  
  try {
    const date = new Date(timeStr);
    if (isNaN(date.getTime())) {
      return timeStr; // 如果无法解析，返回原始字符串
    }
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    return `${year}-${month}-${day} 08:00`;
  } catch (error) {
    console.warn('时间格式化失败:', error);
    return timeStr;
  }
};

// 获取标签颜色类名
const getTagColorClass = (index: number) => {
  const colorClasses = [
    'tag-red',
    'tag-orange', 
    'tag-yellow',
    'tag-green',
    'tag-cyan',
    'tag-blue',
    'tag-purple',
    'tag-pink',
    'tag-indigo',
    'tag-teal'
  ];
  return colorClasses[index % colorClasses.length];
};

// 处理重试
const handleRetry = () => {
  emit('retry');
  if (props.leaderId) {
    fetchLeaderDetail();
  }
};

// 处理返回上一级
const handleGoBack = () => {
  emit('goBack');
  // 使用路由返回上一页
  if (window.history.length > 1) {
    router.go(-1);
  } else {
    // 如果没有历史记录，根据API类型返回对应的列表页
    switch (props.apiType) {
      case 'doctor':
        router.push('/doctor');
        break;
      case 'medicine':
        router.push('/medicine');
        break;
      case 'dept':
        router.push('/dept');
        break;
      case 'mainDept':
        router.push('/main-dept');
        break;
      case 'leader':
      default:
        router.push('/leader');
        break;
    }
  }
};

// 暴露方法给父组件
defineExpose({
  refresh: fetchLeaderDetail,
  leaderData: computed(() => dataSource.value),
  goBack: handleGoBack
});
</script>

<style scoped>
.leader-detail-card {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 16px;
  overflow: hidden;
}

/* 加载和错误状态 */
.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  min-height: 300px;
}

.loading-text {
  margin-top: 16px;
  color: #666;
  font-size: 14px;
}

.error-text {
  color: #f56565;
  margin-bottom: 16px;
}

.retry-btn {
  margin-top: 16px;
}

/* 内容区域 */
.content {
  padding: 6px;
}

/* 第1部分：缩略图区域 */
.thumbnail-section {
  background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
  border-radius: 16px;
  margin-bottom: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.thumbnail-content {
  margin-top: 16px;
  padding: 2px;
}

.large-avatar {
  position: relative;
  width: 100%;
  height: 300px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease;
}

.large-avatar:hover {
  transform: scale(1.02);
}

.large-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(0, 0, 0, 0.4), transparent);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.large-avatar:hover .avatar-overlay {
  opacity: 1;
}

/* 第2部分：统一的信息区域 */
.info-section {
  background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
  border-radius: 16px;
  padding: 12px;
  margin-bottom: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.meta-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
  padding-bottom: 12px;
  padding-top: 12px;
  border-bottom: 1px solid #e2e8f0;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.meta-text {
  font-size: 13px;
  color: #999;
  line-height: 1;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.tag-item {
  margin: 0;
  font-weight: 400;
  font-size: 12px;
  padding: 4px 8px;
  border: none;
  border-radius: 12px;
  color: white;
  transition: all 0.2s ease;
  box-shadow: none;
}

.tag-item:hover {
  transform: translateY(-1px);
  opacity: 0.9;
}

.tag-blue {
  background: #5b9bd5;
}

.tag-purple {
  background: #9b59b6;
}

.tag-green {
  background: #27ae60;
}

.tag-orange {
  background: #e67e22;
}

.tag-default {
  background: #95a5a6;
  color: white;
}

/* 动态标签的鲜艳颜色 */
.tag-red {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
}

.tag-yellow {
  background: linear-gradient(135deg, #ffd93d, #ffb74d);
  color: white;
}

.tag-cyan {
  background: linear-gradient(135deg, #4ecdc4, #26d0ce);
  color: white;
}

.tag-pink {
  background: linear-gradient(135deg, #ff6b9d, #c44569);
  color: white;
}

.tag-indigo {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.tag-teal {
  background: linear-gradient(135deg, #4db6ac, #26a69a);
  color: white;
}

/* 第4部分：描述信息区域 */
.description-section {
  background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
  border-radius: 16px;
  padding: 12px;
  margin-bottom: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  overflow: hidden; /* 防止内容溢出 */
  word-wrap: break-word; /* 强制换行 */
  box-sizing: border-box; /* 包含内边距在宽度计算中 */
}

/* 第5部分：富文本区域 */
.richtext-section {
  background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
  border-radius: 16px;
  padding: 8px;
  margin-bottom: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  overflow: hidden; /* 防止内容溢出 */
  word-wrap: break-word; /* 强制换行 */
  box-sizing: border-box; /* 包含内边距在宽度计算中 */
}

/* 通用区域样式 */
.section-header {
  display: flex;
  align-items: center;
  justify-content: flex-start; /* 确保内容左对齐 */
  gap: 8px;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 2px solid #e2e8f0;
  direction: ltr !important; /* 强制从左到右的文本方向 */
  text-align: left !important; /* 强制左对齐 */
  margin: 10px;
}

.section-header .van-icon {
  order: 1; /* 确保图标在最左侧 */
  flex-shrink: 0; /* 防止图标被压缩 */
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
  text-align: left !important; /* 强制标题左对齐 */
  direction: ltr !important; /* 强制从左到右的文本方向 */
  flex: 1; /* 让标题占据剩余空间 */
  order: 2; /* 确保标题在图标之后 */
}

.section-content {
  color: #4a5568;
  line-height: 1.7;
  word-wrap: break-word; /* 强制长单词换行 */
  word-break: break-all; /* 允许在任意字符间换行 */
  overflow-wrap: break-word; /* 现代浏览器的换行属性 */
  white-space: pre-wrap; /* 保留空白符并自动换行 */
  max-width: 100%; /* 确保不超出容器 */
  box-sizing: border-box; /* 包含内边距在宽度计算中 */
}

.desc-content {
  font-size: 15px;
  line-height: 1.8;
  color: #4a5568;
  text-align: justify;
  word-wrap: break-word; /* 强制长单词换行 */
  word-break: break-all; /* 允许在任意字符间换行 */
  overflow-wrap: break-word; /* 现代浏览器的换行属性 */
  white-space: pre-wrap; /* 保留空白符并自动换行 */
  max-width: 100%; /* 确保不超出容器 */
  box-sizing: border-box; /* 包含内边距在宽度计算中 */
}

/* 富文本内容样式 */
.rich-content {
  font-size: 15px;
  line-height: 1.8;
  color: #4a5568;
  word-wrap: break-word; /* 强制长单词换行 */
  word-break: break-all; /* 允许在任意字符间换行 */
  overflow-wrap: break-word; /* 现代浏览器的换行属性 */
  white-space: pre-wrap; /* 保留空白符并自动换行 */
  max-width: 100%; /* 确保不超出容器 */
  box-sizing: border-box; /* 包含内边距在宽度计算中 */
}

.rich-content :deep(img) {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 12px 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.rich-content :deep(p) {
  margin: 12px 0;
  text-align: justify;
  word-wrap: break-word;
  word-break: break-all;
  overflow-wrap: break-word;
  white-space: pre-wrap;
}

.rich-content :deep(section) {
  margin: 16px 0;
  word-wrap: break-word;
  word-break: break-all;
  overflow-wrap: break-word;
  white-space: pre-wrap;
}

.rich-content :deep(span) {
  line-height: 1.8;
  word-wrap: break-word;
  word-break: break-all;
  overflow-wrap: break-word;
  white-space: pre-wrap;
}

.rich-content :deep(div) {
  word-wrap: break-word;
  word-break: break-all;
  overflow-wrap: break-word;
  white-space: pre-wrap;
}

/* 处理富文本中可能的表格溢出 */
.rich-content :deep(table) {
  max-width: 100%;
  table-layout: fixed;
  word-wrap: break-word;
}

.rich-content :deep(td),
.rich-content :deep(th) {
  word-wrap: break-word;
  word-break: break-all;
  overflow-wrap: break-word;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .large-avatar {
    height: 250px;
  }
  
  .section-title {
    font-size: 24px;
  }
}

@media (max-width: 480px) {
  
  .thumbnail-section,
  .tags-section,
  .description-section,
  .richtext-section {
    margin-bottom: 16px;
  }
  
  
  .large-avatar {
    height: 200px;
  }
  
  .section-title {
    font-size: 22px;
  }
}
</style> 